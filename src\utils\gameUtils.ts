import {
  Node,
  Port,
  Connection,
  NodeType,
  PortDirection,
  PortShape,
  PortColor,
  ValidationResult,
  DEFAULT_PORT_RULES
} from '../types/game';
import { validateDAGDetailed } from './dagValidation';

// Generate unique IDs
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Create a new port
export const createPort = (
  nodeId: string,
  direction: PortDirection,
  shape: PortShape,
  color: PortColor,
  position: { x: number; y: number }
): Port => ({
  id: generateId(),
  shape,
  color,
  direction,
  nodeId,
  position,
  isConnected: false
});

// Create a new node
export const createNode = (
  type: NodeType,
  position: { x: number; y: number },
  inputPortConfigs: Array<{ shape: PortShape; color: PortColor }> = [],
  outputPortConfigs: Array<{ shape: PortShape; color: PortColor }> = []
): Node => {
  const nodeId = generateId();
  
  const inputPorts: Port[] = inputPortConfigs.map((config, index) => 
    createPort(
      nodeId,
      PortDirection.INPUT,
      config.shape,
      config.color,
      { x: -20, y: index * 30 - (inputPortConfigs.length - 1) * 15 }
    )
  );

  const outputPorts: Port[] = outputPortConfigs.map((config, index) => 
    createPort(
      nodeId,
      PortDirection.OUTPUT,
      config.shape,
      config.color,
      { x: 20, y: index * 30 - (outputPortConfigs.length - 1) * 15 }
    )
  );

  return {
    id: nodeId,
    type,
    position,
    inputPorts,
    outputPorts,
    isPlaced: false
  };
};

// Create a start node (only output ports)
export const createStartNode = (
  position: { x: number; y: number },
  outputPortConfigs: Array<{ shape: PortShape; color: PortColor }>
): Node => {
  return createNode(NodeType.START, position, [], outputPortConfigs);
};

// Create an end node (only input ports)
export const createEndNode = (
  position: { x: number; y: number },
  inputPortConfigs: Array<{ shape: PortShape; color: PortColor }>
): Node => {
  return createNode(NodeType.END, position, inputPortConfigs, []);
};

// Check if two ports can be connected
export const canPortsConnect = (port1: Port, port2: Port): boolean => {
  // Must be different directions
  if (port1.direction === port2.direction) return false;
  
  // Must be from different nodes
  if (port1.nodeId === port2.nodeId) return false;
  
  // Check if shapes and colors match according to rules
  const rule = DEFAULT_PORT_RULES.find(
    r => r.shape === port1.shape && r.color === port1.color
  );
  
  if (!rule) return false;
  
  return rule.canConnectTo.some(
    allowed => allowed.shape === port2.shape && allowed.color === port2.color
  );
};

// Create a connection between two ports
export const createConnection = (fromPort: Port, toPort: Port): Connection | null => {
  if (!canPortsConnect(fromPort, toPort)) return null;
  
  // Ensure fromPort is output and toPort is input
  const outputPort = fromPort.direction === PortDirection.OUTPUT ? fromPort : toPort;
  const inputPort = fromPort.direction === PortDirection.INPUT ? fromPort : toPort;
  
  return {
    id: generateId(),
    fromPortId: outputPort.id,
    toPortId: inputPort.id,
    fromNodeId: outputPort.nodeId,
    toNodeId: inputPort.nodeId,
    isValid: true
  };
};

// Validate DAG (Directed Acyclic Graph) - Enhanced version
export const validateDAG = (
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
): ValidationResult => {
  const detailedResult = validateDAGDetailed(nodes, connections);

  // Convert detailed result to basic ValidationResult for backward compatibility
  return {
    isValid: detailedResult.isValid,
    errors: detailedResult.errors,
    executionOrder: detailedResult.executionOrder,
    cycles: detailedResult.cycles
  };
};

// Export the detailed validation for components that need more information
export { validateDAGDetailed } from './dagValidation';

// Calculate node depths for visualization
export const calculateNodeDepths = (
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
): Map<string, number> => {
  const depths = new Map<string, number>();
  const validation = validateDAG(nodes, connections);
  
  if (!validation.isValid) {
    return depths;
  }
  
  // Process nodes in execution order
  for (const nodeId of validation.executionOrder) {
    const node = nodes.get(nodeId);
    if (!node) continue;
    
    if (node.type === NodeType.START) {
      depths.set(nodeId, 0);
    } else {
      // Find maximum depth of input nodes + 1
      let maxInputDepth = -1;
      
      for (const inputPort of node.inputPorts) {
        if (inputPort.isConnected && inputPort.connectedPortId) {
          // Find the connection and source node
          for (const [, connection] of connections) {
            if (connection.toPortId === inputPort.id) {
              const sourceDepth = depths.get(connection.fromNodeId) || 0;
              maxInputDepth = Math.max(maxInputDepth, sourceDepth);
              break;
            }
          }
        }
      }
      
      depths.set(nodeId, maxInputDepth + 1);
    }
  }
  
  return depths;
};

// Get all ports from a node
export const getAllPorts = (node: Node): Port[] => {
  return [...node.inputPorts, ...node.outputPorts];
};

// Find port by ID in a node
export const findPortInNode = (node: Node, portId: string): Port | undefined => {
  return getAllPorts(node).find(port => port.id === portId);
};

// Update port connection status
export const updatePortConnection = (
  port: Port, 
  isConnected: boolean, 
  connectedPortId?: string
): Port => {
  return {
    ...port,
    isConnected,
    connectedPortId: isConnected ? connectedPortId : undefined
  };
};

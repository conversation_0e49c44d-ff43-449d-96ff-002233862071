import { Node, Connection } from '../types/game';

export interface Point {
  x: number;
  y: number;
}

export interface ConnectionPath {
  pathData: string;
  length: number;
  midPoint: Point;
  controlPoints: Point[];
}

export enum RoutingStyle {
  BEZIER = 'bezier',
  ORTHOGONAL = 'orthogonal',
  STRAIGHT = 'straight',
  SMOOTH_STEP = 'smooth-step'
}

// Calculate connection path with different routing styles
export const calculateConnectionPath = (
  fromPos: Point,
  toPos: Point,
  style: RoutingStyle = RoutingStyle.BEZIER,
  obstacles: Node[] = []
): ConnectionPath => {
  switch (style) {
    case RoutingStyle.BEZIER:
      return calculateBezierPath(fromPos, toPos);
    case RoutingStyle.ORTHOGONAL:
      return calculateOrthogonalPath(fromPos, toPos, obstacles);
    case RoutingStyle.STRAIGHT:
      return calculateStraightPath(fromPos, toPos);
    case RoutingStyle.SMOOTH_STEP:
      return calculateSmoothStepPath(fromPos, toPos);
    default:
      return calculateBezierPath(fromPos, toPos);
  }
};

// Bezier curve path (default)
function calculateBezierPath(fromPos: Point, toPos: Point): ConnectionPath {
  const dx = toPos.x - fromPos.x;
  const dy = toPos.y - fromPos.y;
  const distance = Math.sqrt(dx * dx + dy * dy);
  
  // Dynamic control point offset based on distance and direction
  const baseOffset = Math.min(distance * 0.4, 120);
  const verticalInfluence = Math.abs(dy) / distance;
  const controlOffset = baseOffset * (1 + verticalInfluence * 0.3);
  
  const cp1x = fromPos.x + controlOffset;
  const cp1y = fromPos.y;
  const cp2x = toPos.x - controlOffset;
  const cp2y = toPos.y;
  
  const pathData = `M ${fromPos.x} ${fromPos.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPos.x} ${toPos.y}`;
  
  const midPoint = calculateBezierMidpoint(fromPos, { x: cp1x, y: cp1y }, { x: cp2x, y: cp2y }, toPos);
  
  return {
    pathData,
    length: estimateBezierLength(fromPos, { x: cp1x, y: cp1y }, { x: cp2x, y: cp2y }, toPos),
    midPoint,
    controlPoints: [{ x: cp1x, y: cp1y }, { x: cp2x, y: cp2y }]
  };
}

// Orthogonal path (right angles)
function calculateOrthogonalPath(fromPos: Point, toPos: Point, obstacles: Node[]): ConnectionPath {
  const points: Point[] = [fromPos];
  
  // Simple orthogonal routing - can be enhanced with A* pathfinding
  const midX = (fromPos.x + toPos.x) / 2;
  
  if (Math.abs(fromPos.y - toPos.y) > 20) {
    // Add intermediate points for vertical offset
    points.push({ x: midX, y: fromPos.y });
    points.push({ x: midX, y: toPos.y });
  }
  
  points.push(toPos);
  
  // Convert points to path
  let pathData = `M ${points[0].x} ${points[0].y}`;
  for (let i = 1; i < points.length; i++) {
    pathData += ` L ${points[i].x} ${points[i].y}`;
  }
  
  const length = calculatePolylineLength(points);
  const midPoint = points[Math.floor(points.length / 2)];
  
  return {
    pathData,
    length,
    midPoint,
    controlPoints: points.slice(1, -1)
  };
}

// Straight line path
function calculateStraightPath(fromPos: Point, toPos: Point): ConnectionPath {
  const pathData = `M ${fromPos.x} ${fromPos.y} L ${toPos.x} ${toPos.y}`;
  const length = Math.sqrt(Math.pow(toPos.x - fromPos.x, 2) + Math.pow(toPos.y - fromPos.y, 2));
  const midPoint = {
    x: (fromPos.x + toPos.x) / 2,
    y: (fromPos.y + toPos.y) / 2
  };
  
  return {
    pathData,
    length,
    midPoint,
    controlPoints: []
  };
}

// Smooth step path (combination of straight and curved segments)
function calculateSmoothStepPath(fromPos: Point, toPos: Point): ConnectionPath {
  const dx = toPos.x - fromPos.x;
  const dy = toPos.y - fromPos.y;
  
  const stepX = fromPos.x + dx * 0.7;
  const cornerRadius = 15;
  
  let pathData = `M ${fromPos.x} ${fromPos.y}`;
  
  if (Math.abs(dy) > cornerRadius * 2) {
    // Horizontal segment
    pathData += ` L ${stepX - cornerRadius} ${fromPos.y}`;
    
    // Curved corner
    pathData += ` Q ${stepX} ${fromPos.y} ${stepX} ${fromPos.y + (dy > 0 ? cornerRadius : -cornerRadius)}`;
    
    // Vertical segment
    pathData += ` L ${stepX} ${toPos.y - (dy > 0 ? cornerRadius : -cornerRadius)}`;
    
    // Curved corner
    pathData += ` Q ${stepX} ${toPos.y} ${stepX + cornerRadius} ${toPos.y}`;
    
    // Final horizontal segment
    pathData += ` L ${toPos.x} ${toPos.y}`;
  } else {
    // Simple curved path for small vertical differences
    const cp1x = fromPos.x + dx * 0.5;
    const cp1y = fromPos.y;
    const cp2x = fromPos.x + dx * 0.5;
    const cp2y = toPos.y;
    
    pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPos.x} ${toPos.y}`;
  }
  
  const midPoint = {
    x: (fromPos.x + toPos.x) / 2,
    y: (fromPos.y + toPos.y) / 2
  };
  
  return {
    pathData,
    length: Math.abs(dx) + Math.abs(dy), // Approximate length
    midPoint,
    controlPoints: [{ x: stepX, y: fromPos.y }, { x: stepX, y: toPos.y }]
  };
}

// Helper function to calculate bezier midpoint
function calculateBezierMidpoint(p0: Point, p1: Point, p2: Point, p3: Point): Point {
  // Calculate point at t = 0.5 on cubic bezier curve
  const t = 0.5;
  const mt = 1 - t;
  const mt2 = mt * mt;
  const mt3 = mt2 * mt;
  const t2 = t * t;
  const t3 = t2 * t;
  
  return {
    x: mt3 * p0.x + 3 * mt2 * t * p1.x + 3 * mt * t2 * p2.x + t3 * p3.x,
    y: mt3 * p0.y + 3 * mt2 * t * p1.y + 3 * mt * t2 * p2.y + t3 * p3.y
  };
}

// Estimate bezier curve length
function estimateBezierLength(p0: Point, p1: Point, p2: Point, p3: Point): number {
  // Simple approximation using control polygon
  const chord = Math.sqrt(Math.pow(p3.x - p0.x, 2) + Math.pow(p3.y - p0.y, 2));
  const controlNet = 
    Math.sqrt(Math.pow(p1.x - p0.x, 2) + Math.pow(p1.y - p0.y, 2)) +
    Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)) +
    Math.sqrt(Math.pow(p3.x - p2.x, 2) + Math.pow(p3.y - p2.y, 2));
  
  return (chord + controlNet) / 2;
}

// Calculate polyline length
function calculatePolylineLength(points: Point[]): number {
  let length = 0;
  for (let i = 1; i < points.length; i++) {
    length += Math.sqrt(
      Math.pow(points[i].x - points[i-1].x, 2) + 
      Math.pow(points[i].y - points[i-1].y, 2)
    );
  }
  return length;
}

// Check if two line segments intersect
export const doLinesIntersect = (
  line1Start: Point,
  line1End: Point,
  line2Start: Point,
  line2End: Point
): boolean => {
  const det = (line1End.x - line1Start.x) * (line2End.y - line2Start.y) - 
              (line2End.x - line2Start.x) * (line1End.y - line1Start.y);
  
  if (det === 0) return false; // Lines are parallel
  
  const lambda = ((line2End.y - line2Start.y) * (line2End.x - line1Start.x) + 
                  (line2Start.x - line2End.x) * (line2End.y - line1Start.y)) / det;
  
  const gamma = ((line1Start.y - line1End.y) * (line2End.x - line1Start.x) + 
                 (line1End.x - line1Start.x) * (line2End.y - line1Start.y)) / det;
  
  return (0 < lambda && lambda < 1) && (0 < gamma && gamma < 1);
};

// Find optimal routing style based on node positions
export const getOptimalRoutingStyle = (
  fromPos: Point,
  toPos: Point,
  obstacles: Node[]
): RoutingStyle => {
  const dx = Math.abs(toPos.x - fromPos.x);
  const dy = Math.abs(toPos.y - fromPos.y);
  
  // Use orthogonal for large vertical differences
  if (dy > dx * 1.5) {
    return RoutingStyle.ORTHOGONAL;
  }
  
  // Use smooth step for medium distances
  if (dx > 200 && dy > 50) {
    return RoutingStyle.SMOOTH_STEP;
  }
  
  // Default to bezier for most cases
  return RoutingStyle.BEZIER;
};

// Animate connection drawing
export const createConnectionAnimation = (pathData: string, duration: number = 1000) => {
  return {
    strokeDasharray: '1000',
    strokeDashoffset: '1000',
    animation: `draw-connection ${duration}ms ease-out forwards`
  };
};

// CSS animation for connection drawing (to be added to CSS)
export const connectionAnimationCSS = `
@keyframes draw-connection {
  to {
    stroke-dashoffset: 0;
  }
}
`;

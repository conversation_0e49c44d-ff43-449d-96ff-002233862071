.game-area {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.game-svg {
  display: block;
  background: transparent;
  cursor: default;
}

.blueprint-area {
  /* Blueprint area styling */
  transition: all 0.3s ease;
}

.blueprint-area:hover {
  fill: url(#grid);
  opacity: 0.8;
}

.pool-area {
  /* Pool area styling */
  stroke: rgba(255, 255, 255, 0.2);
  stroke-width: 1;
}

.area-separator {
  /* Separator line between areas */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.validation-indicator {
  /* Validation status indicator */
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.validation-indicator:hover {
  r: 12;
}

/* Node Pool Container */
.node-pool-container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  height: 130px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow-x: auto;
  overflow-y: hidden;
  z-index: 10;
}

.node-pool {
  display: flex;
  align-items: center;
  padding: 10px;
  gap: 15px;
  height: 100%;
  min-width: max-content;
}

.pool-node {
  flex-shrink: 0;
  cursor: grab;
  transition: all 0.2s ease;
  border-radius: 8px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
}

.pool-node:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.pool-node:active {
  cursor: grabbing;
  transform: scale(0.95);
}

/* Drag and drop states */
.game-area.drag-over .blueprint-area {
  fill: rgba(0, 255, 0, 0.1);
  stroke: rgba(0, 255, 0, 0.5);
  stroke-width: 2;
  stroke-dasharray: 10, 5;
  animation: drag-over-pulse 1s infinite;
}

@keyframes drag-over-pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* Game mode specific styling */
.game-area[data-mode="tetris"] {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.game-area[data-mode="turn-based"] {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .node-pool-container {
    height: 100px;
    bottom: 5px;
    left: 5px;
    right: 5px;
  }
  
  .node-pool {
    padding: 5px;
    gap: 10px;
  }
  
  .pool-node {
    padding: 3px;
  }
  
  .game-svg text {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .node-pool-container {
    height: 80px;
  }
  
  .node-pool {
    gap: 8px;
  }
  
  .game-svg text {
    font-size: 8px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .game-area {
    background: #000000;
    border: 3px solid #ffffff;
  }
  
  .pool-area {
    fill: #333333;
    stroke: #ffffff;
    stroke-width: 2;
  }
  
  .area-separator {
    stroke: #ffffff;
    stroke-width: 3;
  }
  
  .node-pool-container {
    background: #222222;
    border-color: #ffffff;
    border-width: 3px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .game-area,
  .blueprint-area,
  .validation-indicator,
  .pool-node {
    transition: none;
  }
  
  .game-area.drag-over .blueprint-area {
    animation: none;
  }
  
  .pool-node:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .node-pool-container {
    display: none;
  }
  
  .game-area {
    background: white;
    color: black;
  }
  
  .game-svg text {
    fill: black;
  }
}

/* Focus styles for accessibility */
.pool-node:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

.validation-indicator:focus {
  outline: 2px solid #ffffff;
  outline-offset: 4px;
}

/* Loading state */
.game-area.loading {
  opacity: 0.7;
  pointer-events: none;
}

.game-area.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  to {
    transform: rotate(360deg);
  }
}

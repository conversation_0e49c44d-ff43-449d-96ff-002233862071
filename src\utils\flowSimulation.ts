import { Node, Connection, NodeType, Port } from '../types/game';
import { validateDAGDetailed } from './dagValidation';
import { findPortInNode } from './gameUtils';

export interface FlowParticle {
  id: string;
  connectionId: string;
  position: number; // 0 to 1 along the connection path
  speed: number;
  color: string;
  size: number;
  data?: any; // Payload being carried
}

export interface FlowState {
  particles: Map<string, FlowParticle>;
  activeConnections: Set<string>;
  completedPaths: string[][];
  isSimulating: boolean;
  simulationSpeed: number;
}

export interface SimulationResult {
  success: boolean;
  completedPaths: string[][];
  totalTime: number;
  errors: string[];
}

// Flow simulation manager
export class FlowSimulator {
  private nodes: Map<string, Node>;
  private connections: Map<string, Connection>;
  private flowState: FlowState;
  private animationFrame: number | null = null;
  private lastUpdateTime: number = 0;
  private onUpdate?: (state: FlowState) => void;

  constructor(
    nodes: Map<string, Node>,
    connections: Map<string, Connection>,
    onUpdate?: (state: FlowState) => void
  ) {
    this.nodes = nodes;
    this.connections = connections;
    this.onUpdate = onUpdate;
    this.flowState = {
      particles: new Map(),
      activeConnections: new Set(),
      completedPaths: [],
      isSimulating: false,
      simulationSpeed: 1.0
    };
  }

  // Start flow simulation
  public startSimulation(): SimulationResult {
    // Validate DAG first
    const validation = validateDAGDetailed(this.nodes, this.connections);
    if (!validation.isValid) {
      return {
        success: false,
        completedPaths: [],
        totalTime: 0,
        errors: validation.errors
      };
    }

    this.flowState.isSimulating = true;
    this.flowState.particles.clear();
    this.flowState.activeConnections.clear();
    this.flowState.completedPaths = [];

    // Create initial particles at start nodes
    this.createInitialParticles();

    // Start animation loop
    this.lastUpdateTime = performance.now();
    this.animate();

    return {
      success: true,
      completedPaths: [],
      totalTime: 0,
      errors: []
    };
  }

  // Stop simulation
  public stopSimulation(): void {
    this.flowState.isSimulating = false;
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  // Update simulation speed
  public setSpeed(speed: number): void {
    this.flowState.simulationSpeed = Math.max(0.1, Math.min(5.0, speed));
  }

  // Get current flow state
  public getFlowState(): FlowState {
    return { ...this.flowState };
  }

  // Animation loop
  private animate = (): void => {
    if (!this.flowState.isSimulating) return;

    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastUpdateTime) / 1000; // Convert to seconds
    this.lastUpdateTime = currentTime;

    this.updateParticles(deltaTime);
    this.onUpdate?.(this.flowState);

    this.animationFrame = requestAnimationFrame(this.animate);
  };

  // Create initial particles at start nodes
  private createInitialParticles(): void {
    for (const [nodeId, node] of this.nodes) {
      if (node.type === NodeType.START) {
        // Create particles for each output port
        for (const outputPort of node.outputPorts) {
          if (outputPort.isConnected) {
            // Find connection from this port
            for (const [connectionId, connection] of this.connections) {
              if (connection.fromPortId === outputPort.id) {
                this.createParticle(connection, outputPort);
                this.flowState.activeConnections.add(connectionId);
                break;
              }
            }
          }
        }
      }
    }
  }

  // Create a new flow particle
  private createParticle(connection: Connection, sourcePort: Port): void {
    const particle: FlowParticle = {
      id: `particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      connectionId: connection.id,
      position: 0,
      speed: 0.3 + Math.random() * 0.2, // Random speed variation
      color: sourcePort.color,
      size: 3 + Math.random() * 2,
      data: {
        sourceNodeId: connection.fromNodeId,
        sourcePortId: connection.fromPortId,
        timestamp: Date.now()
      }
    };

    this.flowState.particles.set(particle.id, particle);
  }

  // Update all particles
  private updateParticles(deltaTime: number): void {
    const particlesToRemove: string[] = [];
    const newParticles: FlowParticle[] = [];

    for (const [particleId, particle] of this.flowState.particles) {
      // Update particle position
      particle.position += particle.speed * deltaTime * this.flowState.simulationSpeed;

      if (particle.position >= 1.0) {
        // Particle reached end of connection
        particlesToRemove.push(particleId);
        
        const connection = this.connections.get(particle.connectionId);
        if (connection) {
          this.handleParticleArrival(particle, connection, newParticles);
        }
      }
    }

    // Remove completed particles
    for (const particleId of particlesToRemove) {
      this.flowState.particles.delete(particleId);
    }

    // Add new particles
    for (const newParticle of newParticles) {
      this.flowState.particles.set(newParticle.id, newParticle);
    }

    // Check if simulation is complete
    if (this.flowState.particles.size === 0 && this.flowState.activeConnections.size === 0) {
      this.stopSimulation();
    }
  }

  // Handle particle arrival at a node
  private handleParticleArrival(
    particle: FlowParticle,
    connection: Connection,
    newParticles: FlowParticle[]
  ): void {
    const targetNode = this.nodes.get(connection.toNodeId);
    if (!targetNode) return;

    const targetPort = findPortInNode(targetNode, connection.toPortId);
    if (!targetPort) return;

    if (targetNode.type === NodeType.END) {
      // Particle reached an end node - record completed path
      this.recordCompletedPath(particle);
    } else {
      // Particle reached a regular node - propagate to output ports
      this.propagateParticle(particle, targetNode, newParticles);
    }
  }

  // Record a completed flow path
  private recordCompletedPath(particle: FlowParticle): void {
    // Trace back the path this particle took
    const path = this.tracePath(particle);
    this.flowState.completedPaths.push(path);
  }

  // Propagate particle to output ports of a node
  private propagateParticle(
    particle: FlowParticle,
    node: Node,
    newParticles: FlowParticle[]
  ): void {
    // Simple propagation: send to all connected output ports
    for (const outputPort of node.outputPorts) {
      if (outputPort.isConnected) {
        // Find connection from this port
        for (const [connectionId, connection] of this.connections) {
          if (connection.fromPortId === outputPort.id) {
            const newParticle = this.createDerivedParticle(particle, connection, outputPort);
            newParticles.push(newParticle);
            this.flowState.activeConnections.add(connectionId);
            break;
          }
        }
      }
    }
  }

  // Create a new particle derived from an existing one
  private createDerivedParticle(
    sourceParticle: FlowParticle,
    connection: Connection,
    outputPort: Port
  ): FlowParticle {
    return {
      id: `particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      connectionId: connection.id,
      position: 0,
      speed: sourceParticle.speed * (0.9 + Math.random() * 0.2), // Slight speed variation
      color: outputPort.color,
      size: sourceParticle.size * 0.95, // Slightly smaller
      data: {
        ...sourceParticle.data,
        parentParticleId: sourceParticle.id,
        hops: (sourceParticle.data?.hops || 0) + 1
      }
    };
  }

  // Trace the path a particle took
  private tracePath(particle: FlowParticle): string[] {
    const path: string[] = [];
    
    // This is a simplified path tracing
    // In a more complex implementation, you'd maintain a full path history
    if (particle.data?.sourceNodeId) {
      path.push(particle.data.sourceNodeId);
    }
    
    const connection = this.connections.get(particle.connectionId);
    if (connection) {
      path.push(connection.toNodeId);
    }
    
    return path;
  }
}

// Utility functions for flow visualization

// Calculate particle position along a connection path
export const calculateParticlePosition = (
  fromPos: { x: number; y: number },
  toPos: { x: number; y: number },
  progress: number // 0 to 1
): { x: number; y: number } => {
  return {
    x: fromPos.x + (toPos.x - fromPos.x) * progress,
    y: fromPos.y + (toPos.y - fromPos.y) * progress
  };
};

// Calculate particle position along a bezier curve
export const calculateParticlePositionBezier = (
  p0: { x: number; y: number },
  p1: { x: number; y: number },
  p2: { x: number; y: number },
  p3: { x: number; y: number },
  t: number // 0 to 1
): { x: number; y: number } => {
  const mt = 1 - t;
  const mt2 = mt * mt;
  const mt3 = mt2 * mt;
  const t2 = t * t;
  const t3 = t2 * t;

  return {
    x: mt3 * p0.x + 3 * mt2 * t * p1.x + 3 * mt * t2 * p2.x + t3 * p3.x,
    y: mt3 * p0.y + 3 * mt2 * t * p1.y + 3 * mt * t2 * p2.y + t3 * p3.y
  };
};

// Create flow visualization data for rendering
export const createFlowVisualization = (
  flowState: FlowState,
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
): Array<{
  particleId: string;
  position: { x: number; y: number };
  color: string;
  size: number;
}> => {
  const visualizations: Array<{
    particleId: string;
    position: { x: number; y: number };
    color: string;
    size: number;
  }> = [];

  for (const [particleId, particle] of flowState.particles) {
    const connection = connections.get(particle.connectionId);
    if (!connection) continue;

    const fromNode = nodes.get(connection.fromNodeId);
    const toNode = nodes.get(connection.toNodeId);
    if (!fromNode || !toNode) continue;

    const fromPort = findPortInNode(fromNode, connection.fromPortId);
    const toPort = findPortInNode(toNode, connection.toPortId);
    if (!fromPort || !toPort) continue;

    const fromPos = {
      x: fromNode.position.x + fromPort.position.x,
      y: fromNode.position.y + fromPort.position.y
    };

    const toPos = {
      x: toNode.position.x + toPort.position.x,
      y: toNode.position.y + toPort.position.y
    };

    const position = calculateParticlePosition(fromPos, toPos, particle.position);

    visualizations.push({
      particleId,
      position,
      color: particle.color,
      size: particle.size
    });
  }

  return visualizations;
};

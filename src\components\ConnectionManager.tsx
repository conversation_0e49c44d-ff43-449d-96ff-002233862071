import React, { useState, useCallback, useEffect } from 'react';
import { Port, Node, Connection as ConnectionType, PortDirection, GameEventType } from '../types/game';
import { Connection, TempConnection } from './Connection';
import { useGame } from '../context/GameContext';
import { GameActionType } from '../context/GameContext';
import { createConnection, canPortsConnect, findPortInNode } from '../utils/gameUtils';

interface ConnectionManagerProps {
  svgRef: React.RefObject<SVGSVGElement>;
}

export const ConnectionManager: React.FC<ConnectionManagerProps> = ({ svgRef }) => {
  const { gameState, dragState, setDragState, dispatch, emitEvent } = useGame();
  const [tempConnection, setTempConnection] = useState<{
    fromPort: Port;
    fromNode: Node;
    toPosition: { x: number; y: number };
    isValid: boolean;
  } | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Handle mouse move for temporary connection
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!svgRef.current) return;

    const rect = svgRef.current.getBoundingClientRect();
    const mousePos = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    
    setMousePosition(mousePos);

    if (dragState.isDragging && 
        dragState.dragType === 'connection' && 
        dragState.draggedItem?.fromPortId) {
      
      // Find the source port and node
      let sourcePort: Port | null = null;
      let sourceNode: Node | null = null;
      
      for (const [nodeId, node] of gameState.nodes) {
        const port = findPortInNode(node, dragState.draggedItem.fromPortId);
        if (port) {
          sourcePort = port;
          sourceNode = node;
          break;
        }
      }

      if (sourcePort && sourceNode) {
        // Check if mouse is over a valid target port
        let isValid = false;
        for (const [, node] of gameState.nodes) {
          for (const port of [...node.inputPorts, ...node.outputPorts]) {
            if (port.direction === PortDirection.INPUT && 
                canPortsConnect(sourcePort, port) && 
                !port.isConnected) {
              
              const portWorldPos = {
                x: node.position.x + port.position.x,
                y: node.position.y + port.position.y
              };
              
              const distance = Math.sqrt(
                Math.pow(mousePos.x - portWorldPos.x, 2) + 
                Math.pow(mousePos.y - portWorldPos.y, 2)
              );
              
              if (distance < 20) {
                isValid = true;
                break;
              }
            }
          }
          if (isValid) break;
        }

        setTempConnection({
          fromPort: sourcePort,
          fromNode: sourceNode,
          toPosition: mousePos,
          isValid
        });
      }
    }
  }, [dragState, gameState.nodes, svgRef]);

  // Handle mouse up for connection completion
  const handleMouseUp = useCallback((e: MouseEvent) => {
    if (dragState.isDragging && 
        dragState.dragType === 'connection' && 
        dragState.draggedItem?.fromPortId) {
      
      // Find target port under mouse
      let targetPort: Port | null = null;
      let targetNode: Node | null = null;
      
      if (!svgRef.current) return;
      
      const rect = svgRef.current.getBoundingClientRect();
      const mousePos = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };

      for (const [, node] of gameState.nodes) {
        for (const port of node.inputPorts) {
          const portWorldPos = {
            x: node.position.x + port.position.x,
            y: node.position.y + port.position.y
          };
          
          const distance = Math.sqrt(
            Math.pow(mousePos.x - portWorldPos.x, 2) + 
            Math.pow(mousePos.y - portWorldPos.y, 2)
          );
          
          if (distance < 20) {
            targetPort = port;
            targetNode = node;
            break;
          }
        }
        if (targetPort) break;
      }

      // Create connection if valid
      if (targetPort && targetNode) {
        // Find source port
        let sourcePort: Port | null = null;
        for (const [, node] of gameState.nodes) {
          const port = findPortInNode(node, dragState.draggedItem.fromPortId);
          if (port) {
            sourcePort = port;
            break;
          }
        }

        if (sourcePort && canPortsConnect(sourcePort, targetPort)) {
          // Remove existing connection if target port is already connected
          if (targetPort.isConnected && targetPort.connectedPortId) {
            for (const [connId, connection] of gameState.connections) {
              if (connection.toPortId === targetPort.id) {
                dispatch({
                  type: GameActionType.REMOVE_CONNECTION,
                  payload: { connectionId: connId }
                });
                break;
              }
            }
          }

          // Create new connection
          const newConnection = createConnection(sourcePort, targetPort);
          if (newConnection) {
            dispatch({
              type: GameActionType.ADD_CONNECTION,
              payload: { connection: newConnection }
            });

            emitEvent({
              type: GameEventType.CONNECTION_CREATED,
              payload: { connection: newConnection },
              timestamp: Date.now()
            });
          }
        }
      }

      // Reset drag state
      setDragState({
        isDragging: false,
        dragType: 'connection',
        dragPosition: { x: 0, y: 0 }
      });
      setTempConnection(null);
    }
  }, [dragState, gameState, dispatch, emitEvent, setDragState, svgRef]);

  // Add global event listeners
  useEffect(() => {
    if (dragState.isDragging && dragState.dragType === 'connection') {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState, handleMouseMove, handleMouseUp]);

  // Handle connection start
  const handleConnectionStart = useCallback((port: Port) => {
    if (port.direction === PortDirection.OUTPUT) {
      setDragState({
        isDragging: true,
        dragType: 'connection',
        draggedItem: { fromPortId: port.id },
        dragPosition: mousePosition
      });
    }
  }, [setDragState, mousePosition]);

  // Handle connection end
  const handleConnectionEnd = useCallback((port: Port) => {
    // This is handled in the global mouse up handler
  }, []);

  // Handle connection click (for deletion)
  const handleConnectionClick = useCallback((connection: ConnectionType) => {
    dispatch({
      type: GameActionType.REMOVE_CONNECTION,
      payload: { connectionId: connection.id }
    });

    emitEvent({
      type: GameEventType.CONNECTION_REMOVED,
      payload: { connection },
      timestamp: Date.now()
    });
  }, [dispatch, emitEvent]);

  // Get valid target ports for highlighting
  const getValidTargetPorts = useCallback((): string[] => {
    if (!dragState.isDragging || 
        dragState.dragType !== 'connection' || 
        !dragState.draggedItem?.fromPortId) {
      return [];
    }

    let sourcePort: Port | null = null;
    for (const [, node] of gameState.nodes) {
      const port = findPortInNode(node, dragState.draggedItem.fromPortId);
      if (port) {
        sourcePort = port;
        break;
      }
    }

    if (!sourcePort) return [];

    const validPorts: string[] = [];
    for (const [, node] of gameState.nodes) {
      for (const port of node.inputPorts) {
        if (canPortsConnect(sourcePort, port) && !port.isConnected) {
          validPorts.push(port.id);
        }
      }
    }

    return validPorts;
  }, [dragState, gameState.nodes]);

  const validTargetPorts = getValidTargetPorts();

  return (
    <>
      {/* Render all connections */}
      {Array.from(gameState.connections.values()).map(connection => {
        const fromNode = gameState.nodes.get(connection.fromNodeId);
        const toNode = gameState.nodes.get(connection.toNodeId);
        
        if (!fromNode || !toNode) return null;
        
        return (
          <Connection
            key={connection.id}
            connection={connection}
            fromNode={fromNode}
            toNode={toNode}
            isValid={connection.isValid}
            onClick={handleConnectionClick}
          />
        );
      })}
      
      {/* Render temporary connection during drag */}
      {tempConnection && (
        <TempConnection
          fromPort={tempConnection.fromPort}
          fromNode={tempConnection.fromNode}
          toPosition={tempConnection.toPosition}
          isValid={tempConnection.isValid}
        />
      )}
    </>
  );
};

.node {
  cursor: move;
  transition: all 0.2s ease;
  user-select: none;
}

.node--start .node__body {
  fill: #4CAF50;
}

.node--end .node__body {
  fill: #F44336;
}

.node--regular .node__body {
  fill: #2196F3;
}

.node--placed {
  /* Nodes placed in the blueprint area */
}

.node--in-pool {
  /* Nodes in the pool area */
  opacity: 0.9;
}

.node--selected {
  /* Selected nodes */
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

.node--dragging {
  /* Nodes being dragged */
  filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.6));
  transform: scale(1.05);
  z-index: 1000;
}

.node--hovered {
  /* Hovered nodes */
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
  transform: scale(1.02);
}

.node__shadow {
  /* Node shadow */
  opacity: 0.3;
}

.node__body {
  /* Main node rectangle */
  transition: all 0.2s ease;
}

.node__label {
  /* Node type label */
  font-family: 'Arial', sans-serif;
  pointer-events: none;
}

.node__id {
  /* Node ID for debugging */
  font-family: 'Courier New', monospace;
  pointer-events: none;
}

.node__depth {
  /* Depth indicator for DAG visualization */
  font-family: 'Courier New', monospace;
  pointer-events: none;
}

.node__selection-indicator {
  /* Animated selection border */
  animation: selection-pulse 2s infinite;
}

/* Node preview styles */
.node-preview {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 5px;
  margin: 5px;
  transition: all 0.2s ease;
}

.node-preview:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Animations */
@keyframes selection-pulse {
  0%, 100% {
    stroke-opacity: 1;
  }
  50% {
    stroke-opacity: 0.5;
  }
}

@keyframes node-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes node-disappear {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* Node type specific styles */
.node--start {
  /* Start nodes */
}

.node--start .node__body {
  stroke-dasharray: none;
}

.node--end {
  /* End nodes */
}

.node--end .node__body {
  stroke-dasharray: none;
}

.node--regular {
  /* Regular nodes */
}

/* Validation states */
.node--valid {
  /* Valid nodes in a complete DAG */
}

.node--invalid {
  /* Invalid nodes (part of cycle or disconnected) */
  filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.6));
}

.node--invalid .node__body {
  stroke: #ff0000;
  stroke-width: 3;
  animation: error-pulse 1s infinite;
}

@keyframes error-pulse {
  0%, 100% {
    stroke-opacity: 1;
  }
  50% {
    stroke-opacity: 0.5;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .node {
    transform: scale(0.9);
  }
  
  .node--dragging {
    transform: scale(0.95);
  }
  
  .node__label {
    font-size: 10px;
  }
  
  .node__id {
    font-size: 7px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .node__body {
    stroke-width: 3;
  }
  
  .node__label {
    font-weight: 900;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .node {
    transition: none;
  }
  
  .node__selection-indicator {
    animation: none;
  }
  
  .node--invalid .node__body {
    animation: none;
  }
}

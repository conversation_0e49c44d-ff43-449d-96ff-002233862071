import React, { useRef, useCallback, useEffect, useState } from 'react';
import { Node as NodeType, Port, GameMode } from '../types/game';
import { NodeComponent, NodePreview } from './Node';
import { ConnectionManager } from './ConnectionManager';
import { useGame } from '../context/GameContext';
import { GameActionType } from '../context/GameContext';
import { getValidTargetPorts } from './Port';
import './GameArea.css';

interface GameAreaProps {
  width: number;
  height: number;
}

export const GameArea: React.FC<GameAreaProps> = ({ width, height }) => {
  const { gameState, dragState, setDragState, dispatch } = useGame();
  const svgRef = useRef<SVGSVGElement>(null);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  // Layout constants
  const POOL_AREA_HEIGHT = 150;
  const BLUEPRINT_AREA_HEIGHT = height - POOL_AREA_HEIGHT - 20;
  const POOL_AREA_Y = height - POOL_AREA_HEIGHT;

  // Handle node connection start
  const handleConnectionStart = useCallback((port: Port) => {
    // This is handled by the ConnectionManager
  }, []);

  // Handle node connection end
  const handleConnectionEnd = useCallback((port: Port) => {
    // This is handled by the ConnectionManager
  }, []);

  // Handle node selection
  const handleNodeSelect = useCallback((nodeId: string) => {
    setSelectedNodeId(nodeId === selectedNodeId ? null : nodeId);
  }, [selectedNodeId]);

  // Handle dropping nodes from pool to blueprint area
  const handleNodeDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    
    if (!svgRef.current) return;
    
    const rect = svgRef.current.getBoundingClientRect();
    const dropPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    // Check if drop is in blueprint area
    if (dropPosition.y < POOL_AREA_Y) {
      const draggedNodeId = e.dataTransfer.getData('text/plain');
      const draggedNode = gameState.nodes.get(draggedNodeId);
      
      if (draggedNode && !draggedNode.isPlaced) {
        // Place node in blueprint area
        dispatch({
          type: GameActionType.MOVE_NODE,
          payload: { nodeId: draggedNodeId, position: dropPosition }
        });
        
        dispatch({
          type: GameActionType.PLACE_NODE,
          payload: { nodeId: draggedNodeId, isPlaced: true }
        });
      }
    }
  }, [gameState.nodes, dispatch, POOL_AREA_Y]);

  // Handle drag over
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // Get valid target ports for highlighting
  const validTargetPorts = dragState.isDragging && dragState.dragType === 'connection'
    ? getValidTargetPorts(
        gameState.nodes.get(dragState.draggedItem?.fromPortId || '')?.outputPorts[0] || {} as Port,
        Array.from(gameState.nodes.values()).flatMap(node => [...node.inputPorts, ...node.outputPorts])
      ).map(port => port.id)
    : [];

  return (
    <div className="game-area">
      {/* Main SVG area */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="game-svg"
        onDrop={handleNodeDrop}
        onDragOver={handleDragOver}
      >
        {/* Background grid */}
        <defs>
          <pattern
            id="grid"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 20 0 L 0 0 0 20"
              fill="none"
              stroke="rgba(255, 255, 255, 0.1)"
              strokeWidth="1"
            />
          </pattern>
        </defs>
        
        {/* Blueprint area background */}
        <rect
          x="0"
          y="0"
          width={width}
          height={BLUEPRINT_AREA_HEIGHT}
          fill="url(#grid)"
          className="blueprint-area"
        />
        
        {/* Pool area background */}
        <rect
          x="0"
          y={POOL_AREA_Y}
          width={width}
          height={POOL_AREA_HEIGHT}
          fill="rgba(0, 0, 0, 0.2)"
          className="pool-area"
        />
        
        {/* Area separator */}
        <line
          x1="0"
          y1={POOL_AREA_Y}
          x2={width}
          y2={POOL_AREA_Y}
          stroke="rgba(255, 255, 255, 0.3)"
          strokeWidth="2"
          className="area-separator"
        />
        
        {/* Connection Manager */}
        <ConnectionManager svgRef={svgRef} />
        
        {/* Render placed nodes (in blueprint area) */}
        {Array.from(gameState.nodes.values())
          .filter(node => node.isPlaced)
          .map(node => (
            <g key={node.id} onClick={() => handleNodeSelect(node.id)}>
              <NodeComponent
                node={node}
                onConnectionStart={handleConnectionStart}
                onConnectionEnd={handleConnectionEnd}
                isSelected={selectedNodeId === node.id}
                isDragging={dragState.isDragging && dragState.draggedItem?.nodeId === node.id}
                validTargetPorts={validTargetPorts}
              />
            </g>
          ))}
        
        {/* Pool area label */}
        <text
          x="10"
          y={POOL_AREA_Y + 20}
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="14"
          fontWeight="bold"
        >
          Node Pool ({gameState.nodePool.length}/{gameState.poolCapacity})
        </text>
        
        {/* Blueprint area label */}
        <text
          x="10"
          y="20"
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="14"
          fontWeight="bold"
        >
          Blueprint Area
        </text>
        
        {/* Game mode indicator */}
        <text
          x={width - 150}
          y="20"
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="12"
          textAnchor="end"
        >
          Mode: {gameState.mode === GameMode.TETRIS ? 'Tetris' : 'Turn-based'}
        </text>
        
        {/* Level and score */}
        <text
          x={width - 150}
          y="40"
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="12"
          textAnchor="end"
        >
          Level: {gameState.level} | Score: {gameState.score}
        </text>
        
        {/* Time remaining (for Tetris mode) */}
        {gameState.mode === GameMode.TETRIS && gameState.timeRemaining !== undefined && (
          <text
            x={width - 150}
            y="60"
            fill={gameState.timeRemaining < 10 ? "rgba(255, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.8)"}
            fontSize="12"
            textAnchor="end"
          >
            Time: {gameState.timeRemaining}s
          </text>
        )}
        
        {/* Validation status */}
        <circle
          cx={width - 30}
          cy={BLUEPRINT_AREA_HEIGHT - 30}
          r="10"
          fill={gameState.isValid ? "#4CAF50" : "#F44336"}
          className="validation-indicator"
        />
        <text
          x={width - 30}
          y={BLUEPRINT_AREA_HEIGHT - 10}
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="10"
          textAnchor="middle"
        >
          {gameState.isValid ? 'Valid' : 'Invalid'}
        </text>
      </svg>
      
      {/* Node pool (rendered outside SVG for better interaction) */}
      <div className="node-pool-container">
        <NodePool />
      </div>
    </div>
  );
};

// Node Pool Component
const NodePool: React.FC = () => {
  const { gameState, dispatch } = useGame();

  const handleNodeDragStart = useCallback((e: React.DragEvent, nodeId: string) => {
    e.dataTransfer.setData('text/plain', nodeId);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleNodeClick = useCallback((nodeId: string) => {
    // Double-click to auto-place node in blueprint area
    const node = gameState.nodes.get(nodeId);
    if (node && !node.isPlaced) {
      // Find a good position in the blueprint area
      const position = findAvailablePosition(gameState.nodes);
      
      dispatch({
        type: GameActionType.MOVE_NODE,
        payload: { nodeId, position }
      });
      
      dispatch({
        type: GameActionType.PLACE_NODE,
        payload: { nodeId, isPlaced: true }
      });
    }
  }, [gameState.nodes, dispatch]);

  return (
    <div className="node-pool">
      {gameState.nodePool.map(nodeId => {
        const node = gameState.nodes.get(nodeId);
        if (!node) return null;
        
        return (
          <div
            key={nodeId}
            className="pool-node"
            draggable
            onDragStart={(e) => handleNodeDragStart(e, nodeId)}
            onDoubleClick={() => handleNodeClick(nodeId)}
          >
            <NodePreview node={node} scale={0.8} />
          </div>
        );
      })}
    </div>
  );
};

// Helper function to find available position in blueprint area
function findAvailablePosition(nodes: Map<string, NodeType>): { x: number; y: number } {
  const placedNodes = Array.from(nodes.values()).filter(node => node.isPlaced);
  
  // Simple grid-based positioning
  const gridSize = 150;
  const cols = Math.floor(800 / gridSize);
  
  for (let row = 0; row < 10; row++) {
    for (let col = 0; col < cols; col++) {
      const position = {
        x: col * gridSize + gridSize / 2,
        y: row * gridSize + gridSize / 2
      };
      
      // Check if position is free
      const isFree = !placedNodes.some(node => {
        const distance = Math.sqrt(
          Math.pow(node.position.x - position.x, 2) + 
          Math.pow(node.position.y - position.y, 2)
        );
        return distance < gridSize * 0.8;
      });
      
      if (isFree) {
        return position;
      }
    }
  }
  
  // Fallback to random position
  return {
    x: Math.random() * 600 + 100,
    y: Math.random() * 300 + 100
  };
}

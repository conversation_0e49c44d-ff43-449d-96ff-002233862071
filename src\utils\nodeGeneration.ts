import {
  Node,
  NodeType,
  PortShape,
  PortColor,
  GameMode
} from '../types/game';
import {
  createNode,
  createStartNode,
  createEndNode,
  generateId
} from './gameUtils';

export interface GenerationConfig {
  mode: GameMode;
  level: number;
  poolCapacity: number;
  difficulty: 'easy' | 'medium' | 'hard';
  minNodes: number;
  maxNodes: number;
  minConnections: number;
  maxConnections: number;
}

export interface GeneratedNodeSet {
  nodes: Node[];
  solutionPath: string[]; // Node IDs in execution order
  requiredConnections: number;
  estimatedDifficulty: number;
}

// Available port configurations for different difficulty levels
const PORT_CONFIGS = {
  easy: [
    { shape: PortShape.CIRCLE, color: PortColor.RED },
    { shape: PortShape.CIRCLE, color: PortColor.BLUE },
    { shape: PortShape.SQUARE, color: PortColor.GREEN }
  ],
  medium: [
    { shape: PortShape.CIRCLE, color: PortColor.RED },
    { shape: PortShape.CIRCLE, color: PortColor.BLUE },
    { shape: PortShape.SQUARE, color: PortColor.GREEN },
    { shape: PortShape.SQUARE, color: PortColor.YELLOW },
    { shape: PortShape.DIAMOND, color: PortColor.PURPLE }
  ],
  hard: [
    { shape: PortShape.CIRCLE, color: PortColor.RED },
    { shape: PortShape.CIRCLE, color: PortColor.BLUE },
    { shape: PortShape.SQUARE, color: PortColor.GREEN },
    { shape: PortShape.SQUARE, color: PortColor.YELLOW },
    { shape: PortShape.DIAMOND, color: PortColor.PURPLE },
    { shape: PortShape.DIAMOND, color: PortColor.ORANGE },
    { shape: PortShape.TRIANGLE, color: PortColor.RED },
    { shape: PortShape.TRIANGLE, color: PortColor.BLUE }
  ]
};

// Generate a solvable set of nodes
export const generateNodeSet = (config: GenerationConfig): GeneratedNodeSet => {
  const portConfigs = PORT_CONFIGS[config.difficulty];
  const nodeCount = Math.floor(
    Math.random() * (config.maxNodes - config.minNodes + 1) + config.minNodes
  );

  // Start with a simple linear path to ensure solvability
  const solutionPath = generateSolutionPath(nodeCount, portConfigs);
  
  // Add additional nodes to increase complexity
  const additionalNodes = generateAdditionalNodes(
    solutionPath,
    config,
    portConfigs
  );

  // Shuffle nodes to make the puzzle more challenging
  const allNodes = [...solutionPath.nodes, ...additionalNodes];
  shuffleArray(allNodes);

  // Calculate difficulty metrics
  const estimatedDifficulty = calculateDifficulty(allNodes, solutionPath.connections);

  return {
    nodes: allNodes,
    solutionPath: solutionPath.nodeIds,
    requiredConnections: solutionPath.connections,
    estimatedDifficulty
  };
};

// Generate a guaranteed solvable path from start to end
function generateSolutionPath(
  nodeCount: number,
  portConfigs: Array<{ shape: PortShape; color: PortColor }>
): {
  nodes: Node[];
  nodeIds: string[];
  connections: number;
} {
  const nodes: Node[] = [];
  const nodeIds: string[] = [];
  
  // Ensure we have at least one start and one end node
  const startNodeCount = Math.max(1, Math.floor(nodeCount * 0.2));
  const endNodeCount = Math.max(1, Math.floor(nodeCount * 0.2));
  const regularNodeCount = nodeCount - startNodeCount - endNodeCount;

  // Generate start nodes
  for (let i = 0; i < startNodeCount; i++) {
    const outputPorts = selectRandomPorts(portConfigs, 1, 3);
    const startNode = createStartNode({ x: 0, y: 0 }, outputPorts);
    nodes.push(startNode);
    nodeIds.push(startNode.id);
  }

  // Generate regular nodes that can connect in sequence
  for (let i = 0; i < regularNodeCount; i++) {
    const inputPorts = selectRandomPorts(portConfigs, 1, 2);
    const outputPorts = selectRandomPorts(portConfigs, 1, 2);
    
    const regularNode = createNode(
      NodeType.REGULAR,
      { x: 0, y: 0 },
      inputPorts,
      outputPorts
    );
    nodes.push(regularNode);
    nodeIds.push(regularNode.id);
  }

  // Generate end nodes
  for (let i = 0; i < endNodeCount; i++) {
    const inputPorts = selectRandomPorts(portConfigs, 1, 3);
    const endNode = createEndNode({ x: 0, y: 0 }, inputPorts);
    nodes.push(endNode);
    nodeIds.push(endNode.id);
  }

  // Ensure connectivity by making sure port types can connect
  ensureConnectivity(nodes, portConfigs);

  return {
    nodes,
    nodeIds,
    connections: Math.max(nodeCount - 1, 1)
  };
}

// Generate additional nodes to increase puzzle complexity
function generateAdditionalNodes(
  solutionPath: { nodes: Node[]; nodeIds: string[]; connections: number },
  config: GenerationConfig,
  portConfigs: Array<{ shape: PortShape; color: PortColor }>
): Node[] {
  const additionalNodes: Node[] = [];
  const additionalCount = Math.floor(config.poolCapacity * 0.3); // 30% additional nodes

  for (let i = 0; i < additionalCount; i++) {
    const nodeType = Math.random() < 0.8 ? NodeType.REGULAR : 
                    (Math.random() < 0.5 ? NodeType.START : NodeType.END);

    let node: Node;
    
    switch (nodeType) {
      case NodeType.START:
        const startOutputPorts = selectRandomPorts(portConfigs, 1, 2);
        node = createStartNode({ x: 0, y: 0 }, startOutputPorts);
        break;
        
      case NodeType.END:
        const endInputPorts = selectRandomPorts(portConfigs, 1, 2);
        node = createEndNode({ x: 0, y: 0 }, endInputPorts);
        break;
        
      default:
        const inputPorts = selectRandomPorts(portConfigs, 1, 2);
        const outputPorts = selectRandomPorts(portConfigs, 1, 2);
        node = createNode(NodeType.REGULAR, { x: 0, y: 0 }, inputPorts, outputPorts);
        break;
    }

    additionalNodes.push(node);
  }

  return additionalNodes;
}

// Select random port configurations
function selectRandomPorts(
  portConfigs: Array<{ shape: PortShape; color: PortColor }>,
  min: number,
  max: number
): Array<{ shape: PortShape; color: PortColor }> {
  const count = Math.floor(Math.random() * (max - min + 1) + min);
  const selected: Array<{ shape: PortShape; color: PortColor }> = [];

  for (let i = 0; i < count; i++) {
    const randomConfig = portConfigs[Math.floor(Math.random() * portConfigs.length)];
    selected.push(randomConfig);
  }

  return selected;
}

// Ensure nodes can connect by adjusting port types
function ensureConnectivity(
  nodes: Node[],
  portConfigs: Array<{ shape: PortShape; color: PortColor }>
): void {
  // This is a simplified connectivity check
  // In a more sophisticated implementation, you would ensure
  // that there's always a valid path from start to end nodes
  
  for (let i = 0; i < nodes.length - 1; i++) {
    const currentNode = nodes[i];
    const nextNode = nodes[i + 1];

    if (currentNode.outputPorts.length > 0 && nextNode.inputPorts.length > 0) {
      // Ensure at least one output port can connect to one input port
      const outputPort = currentNode.outputPorts[0];
      const inputPort = nextNode.inputPorts[0];

      // Make them compatible
      inputPort.shape = outputPort.shape;
      inputPort.color = outputPort.color;
    }
  }
}

// Calculate estimated difficulty of the puzzle
function calculateDifficulty(nodes: Node[], requiredConnections: number): number {
  let difficulty = 0;

  // Base difficulty from node count
  difficulty += nodes.length * 0.1;

  // Difficulty from port variety
  const uniquePortTypes = new Set<string>();
  nodes.forEach(node => {
    [...node.inputPorts, ...node.outputPorts].forEach(port => {
      uniquePortTypes.add(`${port.shape}-${port.color}`);
    });
  });
  difficulty += uniquePortTypes.size * 0.2;

  // Difficulty from required connections
  difficulty += requiredConnections * 0.15;

  // Difficulty from node type distribution
  const startNodes = nodes.filter(n => n.type === NodeType.START).length;
  const endNodes = nodes.filter(n => n.type === NodeType.END).length;
  const regularNodes = nodes.filter(n => n.type === NodeType.REGULAR).length;
  
  // More balanced distribution = higher difficulty
  const balance = Math.min(startNodes, endNodes, regularNodes) / Math.max(startNodes, endNodes, regularNodes);
  difficulty += balance * 0.3;

  return Math.min(difficulty, 1.0); // Cap at 1.0
}

// Utility function to shuffle array
function shuffleArray<T>(array: T[]): void {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
}

// Generate nodes for specific game modes
export const generateNodesForMode = (
  mode: GameMode,
  level: number,
  poolCapacity: number
): GeneratedNodeSet => {
  const baseConfig: GenerationConfig = {
    mode,
    level,
    poolCapacity,
    difficulty: level <= 3 ? 'easy' : level <= 7 ? 'medium' : 'hard',
    minNodes: Math.max(3, Math.floor(poolCapacity * 0.6)),
    maxNodes: poolCapacity,
    minConnections: 2,
    maxConnections: Math.floor(poolCapacity * 0.8)
  };

  if (mode === GameMode.TETRIS) {
    // Tetris mode: faster, simpler puzzles
    return generateNodeSet({
      ...baseConfig,
      minNodes: Math.max(3, Math.floor(poolCapacity * 0.5)),
      maxNodes: Math.floor(poolCapacity * 0.8),
      difficulty: level <= 5 ? 'easy' : level <= 10 ? 'medium' : 'hard'
    });
  } else {
    // Turn-based mode: more complex puzzles
    return generateNodeSet({
      ...baseConfig,
      minNodes: Math.max(4, Math.floor(poolCapacity * 0.7)),
      maxNodes: poolCapacity,
      difficulty: level <= 2 ? 'easy' : level <= 5 ? 'medium' : 'hard'
    });
  }
};

// Validate that a generated node set is actually solvable
export const validateGeneratedNodes = (nodeSet: GeneratedNodeSet): boolean => {
  // This would implement a more thorough check to ensure
  // the generated nodes can actually form a valid DAG
  // For now, we'll do a basic check
  
  const hasStart = nodeSet.nodes.some(n => n.type === NodeType.START);
  const hasEnd = nodeSet.nodes.some(n => n.type === NodeType.END);
  const hasRegular = nodeSet.nodes.some(n => n.type === NodeType.REGULAR);
  
  return hasStart && hasEnd && nodeSet.nodes.length >= 3;
};

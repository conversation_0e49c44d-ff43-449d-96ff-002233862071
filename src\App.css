/* Game Container */
.game-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Game Header */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.game-header h1 {
  color: white;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.game-controls {
  display: flex;
  gap: 1rem;
}

/* Buttons */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* Game Main */
.game-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Game Footer */
.game-footer {
  padding: 0.5rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.game-stats {
  display: flex;
  gap: 2rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.game-stats span {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-header {
    padding: 0.5rem 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .game-header h1 {
    font-size: 1.4rem;
  }

  .game-controls {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .game-stats {
    gap: 1rem;
    font-size: 0.8rem;
    justify-content: center;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .game-container {
    background: #000000;
  }

  .game-header,
  .game-footer {
    background: #333333;
    border-color: #ffffff;
  }

  .btn-primary {
    background: #00ff00;
    color: #000000;
  }

  .btn-secondary {
    background: #ff0000;
    color: #ffffff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }

  .btn:hover {
    transform: none;
  }
}

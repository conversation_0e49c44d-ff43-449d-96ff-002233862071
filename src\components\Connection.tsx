import React, { useMemo } from 'react';
import { Connection as ConnectionType, Node, Port } from '../types/game';
import { findPortInNode } from '../utils/gameUtils';
import './Connection.css';

interface ConnectionProps {
  connection: ConnectionType;
  fromNode: Node;
  toNode: Node;
  isHighlighted?: boolean;
  isValid?: boolean;
  onClick?: (connection: ConnectionType) => void;
}

export const Connection: React.FC<ConnectionProps> = ({
  connection,
  fromNode,
  toNode,
  isHighlighted = false,
  isValid = true,
  onClick
}) => {
  const { fromPort, toPort, pathData, midPoint } = useMemo(() => {
    const fromPort = findPortInNode(fromNode, connection.fromPortId);
    const toPort = findPortInNode(toNode, connection.toPortId);
    
    if (!fromPort || !toPort) {
      return { fromPort: null, toPort: null, pathData: '', midPoint: { x: 0, y: 0 } };
    }

    // Calculate absolute positions
    const fromPos = {
      x: fromNode.position.x + fromPort.position.x,
      y: fromNode.position.y + fromPort.position.y
    };
    
    const toPos = {
      x: toNode.position.x + toPort.position.x,
      y: toNode.position.y + toPort.position.y
    };

    // Create curved path
    const dx = toPos.x - fromPos.x;
    const dy = toPos.y - fromPos.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Control points for bezier curve
    const controlOffset = Math.min(distance * 0.5, 100);
    const cp1x = fromPos.x + controlOffset;
    const cp1y = fromPos.y;
    const cp2x = toPos.x - controlOffset;
    const cp2y = toPos.y;
    
    const pathData = `M ${fromPos.x} ${fromPos.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPos.x} ${toPos.y}`;
    
    // Calculate midpoint for interaction
    const midPoint = {
      x: (fromPos.x + toPos.x) / 2,
      y: (fromPos.y + toPos.y) / 2
    };

    return { fromPort, toPort, pathData, midPoint };
  }, [connection, fromNode, toNode]);

  if (!fromPort || !toPort) {
    return null;
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.(connection);
  };

  const connectionClasses = [
    'connection',
    isValid ? 'connection--valid' : 'connection--invalid',
    isHighlighted ? 'connection--highlighted' : ''
  ].filter(Boolean).join(' ');

  const strokeColor = isValid ? fromPort.color : '#ff0000';
  const strokeWidth = isHighlighted ? 4 : 2;

  return (
    <g className={connectionClasses}>
      {/* Connection shadow */}
      <path
        d={pathData}
        fill="none"
        stroke="rgba(0, 0, 0, 0.3)"
        strokeWidth={strokeWidth + 1}
        transform="translate(2, 2)"
        className="connection__shadow"
      />
      
      {/* Main connection line */}
      <path
        d={pathData}
        fill="none"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        className="connection__line"
        onClick={handleClick}
        style={{ cursor: 'pointer' }}
      />
      
      {/* Interaction area (invisible wider line for easier clicking) */}
      <path
        d={pathData}
        fill="none"
        stroke="transparent"
        strokeWidth={12}
        className="connection__interaction-area"
        onClick={handleClick}
        style={{ cursor: 'pointer' }}
      />
      
      {/* Connection midpoint indicator */}
      <circle
        cx={midPoint.x}
        cy={midPoint.y}
        r={isHighlighted ? 6 : 4}
        fill={strokeColor}
        stroke="white"
        strokeWidth={1}
        className="connection__midpoint"
        onClick={handleClick}
        style={{ cursor: 'pointer' }}
      />
      
      {/* Flow animation */}
      {isValid && (
        <>
          <circle
            r="3"
            fill="white"
            opacity="0.8"
            className="connection__flow-indicator"
          >
            <animateMotion
              dur="2s"
              repeatCount="indefinite"
              path={pathData}
            />
          </circle>

          {/* Additional flow particles */}
          <circle
            r="2"
            fill={strokeColor}
            opacity="0.6"
            className="connection__flow-particle"
          >
            <animateMotion
              dur="2s"
              repeatCount="indefinite"
              path={pathData}
              begin="0.5s"
            />
          </circle>

          <circle
            r="1.5"
            fill="white"
            opacity="0.4"
            className="connection__flow-particle"
          >
            <animateMotion
              dur="2s"
              repeatCount="indefinite"
              path={pathData}
              begin="1s"
            />
          </circle>
        </>
      )}
      
      {/* Arrow head */}
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={strokeColor}
          />
        </marker>
      </defs>
      
      <path
        d={pathData}
        fill="none"
        stroke="transparent"
        strokeWidth="1"
        markerEnd={`url(#arrowhead-${connection.id})`}
      />
    </g>
  );
};

// Component for temporary connection during drag
interface TempConnectionProps {
  fromPort: Port;
  fromNode: Node;
  toPosition: { x: number; y: number };
  isValid?: boolean;
}

export const TempConnection: React.FC<TempConnectionProps> = ({
  fromPort,
  fromNode,
  toPosition,
  isValid = true
}) => {
  const pathData = useMemo(() => {
    const fromPos = {
      x: fromNode.position.x + fromPort.position.x,
      y: fromNode.position.y + fromPort.position.y
    };
    
    const dx = toPosition.x - fromPos.x;
    const dy = toPosition.y - fromPos.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Control points for bezier curve
    const controlOffset = Math.min(distance * 0.5, 100);
    const cp1x = fromPos.x + controlOffset;
    const cp1y = fromPos.y;
    const cp2x = toPosition.x - controlOffset;
    const cp2y = toPosition.y;
    
    return `M ${fromPos.x} ${fromPos.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPosition.x} ${toPosition.y}`;
  }, [fromPort, fromNode, toPosition]);

  const strokeColor = isValid ? fromPort.color : '#ff0000';

  return (
    <g className="temp-connection">
      <path
        d={pathData}
        fill="none"
        stroke={strokeColor}
        strokeWidth="2"
        strokeDasharray="5,5"
        opacity="0.7"
        className="temp-connection__line"
      />
      
      {/* End point indicator */}
      <circle
        cx={toPosition.x}
        cy={toPosition.y}
        r="4"
        fill={strokeColor}
        opacity="0.7"
        className="temp-connection__endpoint"
      />
    </g>
  );
};

// Utility function to calculate connection path
export const calculateConnectionPath = (
  fromPos: { x: number; y: number },
  toPos: { x: number; y: number }
): string => {
  const dx = toPos.x - fromPos.x;
  const dy = toPos.y - fromPos.y;
  const distance = Math.sqrt(dx * dx + dy * dy);
  
  const controlOffset = Math.min(distance * 0.5, 100);
  const cp1x = fromPos.x + controlOffset;
  const cp1y = fromPos.y;
  const cp2x = toPos.x - controlOffset;
  const cp2y = toPos.y;
  
  return `M ${fromPos.x} ${fromPos.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPos.x} ${toPos.y}`;
};

// Check if two connection paths intersect (for turn-based mode)
export const doConnectionsIntersect = (
  path1: string,
  path2: string
): boolean => {
  // This is a simplified intersection check
  // In a real implementation, you'd use a more sophisticated algorithm
  // For now, we'll return false to allow all connections
  return false;
};

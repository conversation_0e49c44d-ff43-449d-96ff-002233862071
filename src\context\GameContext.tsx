import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { 
  GameState, 
  GameMode, 
  Node, 
  Connection, 
  GameEvent,
  GameEventType,
  DragState
} from '../types/game';
import { validateDAG } from '../utils/gameUtils';

// Action types
export enum GameActionType {
  SET_MODE = 'SET_MODE',
  ADD_NODE = 'ADD_NODE',
  REMOVE_NODE = 'REMOVE_NODE',
  MOVE_NODE = 'MOVE_NODE',
  PLACE_NODE = 'PLACE_NODE',
  ADD_CONNECTION = 'ADD_CONNECTION',
  REMOVE_CONNECTION = 'REMOVE_CONNECTION',
  VALIDATE_BLUEPRINT = 'VALIDATE_BLUEPRINT',
  UPDATE_SCORE = 'UPDATE_SCORE',
  NEXT_LEVEL = 'NEXT_LEVEL',
  SET_TIME_REMAINING = 'SET_TIME_REMAINING',
  RESET_GAME = 'RESET_GAME',
  SET_DRAG_STATE = 'SET_DRAG_STATE'
}

export interface GameAction {
  type: GameActionType;
  payload?: any;
}

// Initial state
const initialGameState: GameState = {
  mode: GameMode.TETRIS,
  nodes: new Map(),
  connections: new Map(),
  nodePool: [],
  blueprintNodes: [],
  startNodes: [],
  endNodes: [],
  isValid: false,
  score: 0,
  level: 1,
  poolCapacity: 5
};

const initialDragState: DragState = {
  isDragging: false,
  dragType: 'node',
  dragPosition: { x: 0, y: 0 }
};

// Game reducer
const gameReducer = (state: GameState, action: GameAction): GameState => {
  switch (action.type) {
    case GameActionType.SET_MODE:
      return {
        ...state,
        mode: action.payload.mode
      };

    case GameActionType.ADD_NODE:
      const newNode: Node = action.payload.node;
      const newNodes = new Map(state.nodes);
      newNodes.set(newNode.id, newNode);
      
      const newNodePool = newNode.isPlaced ? state.nodePool : [...state.nodePool, newNode.id];
      const newBlueprintNodes = newNode.isPlaced ? [...state.blueprintNodes, newNode.id] : state.blueprintNodes;
      
      return {
        ...state,
        nodes: newNodes,
        nodePool: newNodePool,
        blueprintNodes: newBlueprintNodes
      };

    case GameActionType.REMOVE_NODE:
      const nodeIdToRemove = action.payload.nodeId;
      const nodesAfterRemoval = new Map(state.nodes);
      nodesAfterRemoval.delete(nodeIdToRemove);
      
      // Remove connections involving this node
      const connectionsAfterRemoval = new Map(state.connections);
      for (const [connId, connection] of connectionsAfterRemoval) {
        if (connection.fromNodeId === nodeIdToRemove || connection.toNodeId === nodeIdToRemove) {
          connectionsAfterRemoval.delete(connId);
        }
      }
      
      return {
        ...state,
        nodes: nodesAfterRemoval,
        connections: connectionsAfterRemoval,
        nodePool: state.nodePool.filter(id => id !== nodeIdToRemove),
        blueprintNodes: state.blueprintNodes.filter(id => id !== nodeIdToRemove)
      };

    case GameActionType.MOVE_NODE:
      const { nodeId, position } = action.payload;
      const updatedNodes = new Map(state.nodes);
      const nodeToMove = updatedNodes.get(nodeId);
      
      if (nodeToMove) {
        updatedNodes.set(nodeId, {
          ...nodeToMove,
          position
        });
      }
      
      return {
        ...state,
        nodes: updatedNodes
      };

    case GameActionType.PLACE_NODE:
      const { nodeId: nodeToPlaceId, isPlaced } = action.payload;
      const nodesForPlacement = new Map(state.nodes);
      const nodeToPlace = nodesForPlacement.get(nodeToPlaceId);
      
      if (nodeToPlace) {
        nodesForPlacement.set(nodeToPlaceId, {
          ...nodeToPlace,
          isPlaced
        });
        
        const poolAfterPlacement = isPlaced 
          ? state.nodePool.filter(id => id !== nodeToPlaceId)
          : [...state.nodePool, nodeToPlaceId];
          
        const blueprintAfterPlacement = isPlaced
          ? [...state.blueprintNodes, nodeToPlaceId]
          : state.blueprintNodes.filter(id => id !== nodeToPlaceId);
        
        return {
          ...state,
          nodes: nodesForPlacement,
          nodePool: poolAfterPlacement,
          blueprintNodes: blueprintAfterPlacement
        };
      }
      
      return state;

    case GameActionType.ADD_CONNECTION:
      const newConnection: Connection = action.payload.connection;
      const connectionsWithNew = new Map(state.connections);
      connectionsWithNew.set(newConnection.id, newConnection);
      
      // Update port connection status
      const nodesWithUpdatedPorts = new Map(state.nodes);
      const fromNode = nodesWithUpdatedPorts.get(newConnection.fromNodeId);
      const toNode = nodesWithUpdatedPorts.get(newConnection.toNodeId);
      
      if (fromNode && toNode) {
        // Update from port
        const updatedFromNode = {
          ...fromNode,
          outputPorts: fromNode.outputPorts.map(port => 
            port.id === newConnection.fromPortId 
              ? { ...port, isConnected: true, connectedPortId: newConnection.toPortId }
              : port
          )
        };
        
        // Update to port
        const updatedToNode = {
          ...toNode,
          inputPorts: toNode.inputPorts.map(port => 
            port.id === newConnection.toPortId 
              ? { ...port, isConnected: true, connectedPortId: newConnection.fromPortId }
              : port
          )
        };
        
        nodesWithUpdatedPorts.set(newConnection.fromNodeId, updatedFromNode);
        nodesWithUpdatedPorts.set(newConnection.toNodeId, updatedToNode);
      }
      
      return {
        ...state,
        nodes: nodesWithUpdatedPorts,
        connections: connectionsWithNew
      };

    case GameActionType.REMOVE_CONNECTION:
      const connectionIdToRemove = action.payload.connectionId;
      const connectionToRemove = state.connections.get(connectionIdToRemove);
      
      if (!connectionToRemove) return state;
      
      const connectionsAfterConnectionRemoval = new Map(state.connections);
      connectionsAfterConnectionRemoval.delete(connectionIdToRemove);
      
      // Update port connection status
      const nodesAfterConnectionRemoval = new Map(state.nodes);
      const fromNodeAfterRemoval = nodesAfterConnectionRemoval.get(connectionToRemove.fromNodeId);
      const toNodeAfterRemoval = nodesAfterConnectionRemoval.get(connectionToRemove.toNodeId);
      
      if (fromNodeAfterRemoval && toNodeAfterRemoval) {
        // Update from port
        const updatedFromNodeAfterRemoval = {
          ...fromNodeAfterRemoval,
          outputPorts: fromNodeAfterRemoval.outputPorts.map(port => 
            port.id === connectionToRemove.fromPortId 
              ? { ...port, isConnected: false, connectedPortId: undefined }
              : port
          )
        };
        
        // Update to port
        const updatedToNodeAfterRemoval = {
          ...toNodeAfterRemoval,
          inputPorts: toNodeAfterRemoval.inputPorts.map(port => 
            port.id === connectionToRemove.toPortId 
              ? { ...port, isConnected: false, connectedPortId: undefined }
              : port
          )
        };
        
        nodesAfterConnectionRemoval.set(connectionToRemove.fromNodeId, updatedFromNodeAfterRemoval);
        nodesAfterConnectionRemoval.set(connectionToRemove.toNodeId, updatedToNodeAfterRemoval);
      }
      
      return {
        ...state,
        nodes: nodesAfterConnectionRemoval,
        connections: connectionsAfterConnectionRemoval
      };

    case GameActionType.VALIDATE_BLUEPRINT:
      const validation = validateDAG(state.nodes, state.connections);
      return {
        ...state,
        isValid: validation.isValid
      };

    case GameActionType.UPDATE_SCORE:
      return {
        ...state,
        score: action.payload.score
      };

    case GameActionType.NEXT_LEVEL:
      return {
        ...state,
        level: state.level + 1,
        poolCapacity: Math.min(state.poolCapacity + 1, 10) // Increase capacity but cap at 10
      };

    case GameActionType.SET_TIME_REMAINING:
      return {
        ...state,
        timeRemaining: action.payload.time
      };

    case GameActionType.RESET_GAME:
      return {
        ...initialGameState,
        mode: state.mode // Keep the current mode
      };

    default:
      return state;
  }
};

// Drag state reducer
const dragReducer = (state: DragState, action: any): DragState => {
  switch (action.type) {
    case 'SET_DRAG_STATE':
      return {
        ...state,
        ...action.payload
      };
    case 'RESET_DRAG':
      return initialDragState;
    default:
      return state;
  }
};

// Context
interface GameContextType {
  gameState: GameState;
  dragState: DragState;
  dispatch: React.Dispatch<GameAction>;
  setDragState: (dragState: Partial<DragState>) => void;
  emitEvent: (event: GameEvent) => void;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

// Provider component
export const GameProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [gameState, dispatch] = useReducer(gameReducer, initialGameState);
  const [dragState, dragDispatch] = useReducer(dragReducer, initialDragState);

  const setDragState = (newDragState: Partial<DragState>) => {
    dragDispatch({
      type: 'SET_DRAG_STATE',
      payload: newDragState
    });
  };

  const emitEvent = (event: GameEvent) => {
    // Handle game events here
    console.log('Game Event:', event);
    
    // Auto-validate after certain events
    if ([
      GameEventType.CONNECTION_CREATED,
      GameEventType.CONNECTION_REMOVED,
      GameEventType.NODE_PLACED,
      GameEventType.NODE_REMOVED
    ].includes(event.type as GameEventType)) {
      dispatch({ type: GameActionType.VALIDATE_BLUEPRINT });
    }
  };

  return (
    <GameContext.Provider value={{
      gameState,
      dragState,
      dispatch,
      setDragState,
      emitEvent
    }}>
      {children}
    </GameContext.Provider>
  );
};

// Hook to use game context
export const useGame = (): GameContextType => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};

// Core game types and interfaces

export enum PortShape {
  SQUARE = 'square',
  DIAMOND = 'diamond',
  TRIANGLE = 'triangle',
  CIRCLE = 'circle'
}

export enum PortColor {
  RED = '#ff4444',
  BLUE = '#4444ff',
  GREEN = '#44ff44',
  YELLOW = '#ffff44',
  PURPLE = '#ff44ff',
  ORANGE = '#ff8844'
}

export enum PortDirection {
  INPUT = 'input',   // Left side ports
  OUTPUT = 'output'  // Right side ports
}

export enum NodeType {
  START = 'start',     // Only has output ports
  END = 'end',         // Only has input ports
  REGULAR = 'regular'  // Has both input and output ports
}

export enum GameMode {
  TETRIS = 'tetris',
  TURN_BASED = 'turn_based'
}

export interface Port {
  id: string;
  shape: PortShape;
  color: PortColor;
  direction: PortDirection;
  nodeId: string;
  position: { x: number; y: number }; // Relative to node
  isConnected: boolean;
  connectedPortId?: string;
}

export interface Node {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  inputPorts: Port[];
  outputPorts: Port[];
  isPlaced: boolean; // Whether node is in blueprint area or pool
  depth?: number; // For DAG validation, calculated during validation
}

export interface Connection {
  id: string;
  fromPortId: string;
  toPortId: string;
  fromNodeId: string;
  toNodeId: string;
  isValid: boolean;
}

export interface GameState {
  mode: GameMode;
  nodes: Map<string, Node>;
  connections: Map<string, Connection>;
  nodePool: string[]; // IDs of nodes in the pool area
  blueprintNodes: string[]; // IDs of nodes in the blueprint area
  startNodes: string[]; // IDs of start nodes
  endNodes: string[]; // IDs of end nodes
  isValid: boolean; // Whether current blueprint is valid DAG
  score: number;
  level: number;
  timeRemaining?: number; // For tetris mode
  poolCapacity: number; // Maximum nodes in pool
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  executionOrder: string[]; // Node IDs in execution order
  cycles: string[][]; // Any cycles found
}

export interface GameConfig {
  mode: GameMode;
  initialPoolCapacity: number;
  maxNodesPerLevel: number;
  timeLimit?: number; // For tetris mode
  allowCrossing: boolean; // For turn-based mode
}

// Utility types for drag and drop
export interface DragState {
  isDragging: boolean;
  dragType: 'node' | 'connection';
  draggedItem?: {
    nodeId?: string;
    fromPortId?: string;
  };
  dragPosition: { x: number; y: number };
}

// Port matching rules
export interface PortMatchRule {
  shape: PortShape;
  color: PortColor;
  canConnectTo: Array<{ shape: PortShape; color: PortColor }>;
}

export const DEFAULT_PORT_RULES: PortMatchRule[] = [
  {
    shape: PortShape.SQUARE,
    color: PortColor.RED,
    canConnectTo: [{ shape: PortShape.SQUARE, color: PortColor.RED }]
  },
  {
    shape: PortShape.DIAMOND,
    color: PortColor.BLUE,
    canConnectTo: [{ shape: PortShape.DIAMOND, color: PortColor.BLUE }]
  },
  {
    shape: PortShape.TRIANGLE,
    color: PortColor.GREEN,
    canConnectTo: [{ shape: PortShape.TRIANGLE, color: PortColor.GREEN }]
  },
  {
    shape: PortShape.CIRCLE,
    color: PortColor.YELLOW,
    canConnectTo: [{ shape: PortShape.CIRCLE, color: PortColor.YELLOW }]
  }
];

// Game events
export interface GameEvent {
  type: string;
  payload: any;
  timestamp: number;
}

export enum GameEventType {
  NODE_PLACED = 'node_placed',
  NODE_REMOVED = 'node_removed',
  CONNECTION_CREATED = 'connection_created',
  CONNECTION_REMOVED = 'connection_removed',
  VALIDATION_COMPLETED = 'validation_completed',
  LEVEL_COMPLETED = 'level_completed',
  GAME_OVER = 'game_over'
}

.connection {
  transition: all 0.2s ease;
  pointer-events: all;
}

.connection--valid {
  /* Valid connections */
}

.connection--invalid {
  /* Invalid connections */
  filter: drop-shadow(0 0 4px rgba(255, 0, 0, 0.6));
}

.connection--highlighted {
  /* Highlighted connections */
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
}

.connection__shadow {
  /* Connection shadow */
  opacity: 0.3;
}

.connection__line {
  /* Main connection line */
  transition: all 0.2s ease;
  stroke-linecap: round;
}

.connection__line:hover {
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.6));
  stroke-width: 3;
}

.connection__interaction-area {
  /* Invisible area for easier clicking */
  pointer-events: all;
}

.connection__midpoint {
  /* Connection midpoint indicator */
  transition: all 0.2s ease;
}

.connection__midpoint:hover {
  r: 6;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
}

.connection__flow-indicator {
  /* Animated flow indicator */
  pointer-events: none;
}

/* Temporary connection styles */
.temp-connection {
  pointer-events: none;
}

.temp-connection__line {
  /* Temporary connection line during drag */
  animation: temp-connection-pulse 1s infinite;
}

.temp-connection__endpoint {
  /* End point of temporary connection */
  animation: temp-endpoint-pulse 0.5s infinite;
}

/* Animations */
@keyframes temp-connection-pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.4;
  }
}

@keyframes temp-endpoint-pulse {
  0%, 100% {
    r: 4;
    opacity: 0.7;
  }
  50% {
    r: 6;
    opacity: 1;
  }
}

@keyframes flow-animation {
  0% {
    offset-distance: 0%;
  }
  100% {
    offset-distance: 100%;
  }
}

/* Connection validation states */
.connection--cycle {
  /* Connections that create cycles */
  stroke: #ff0000;
  stroke-dasharray: 10, 5;
  animation: cycle-warning 1s infinite;
}

@keyframes cycle-warning {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Connection types based on port colors */
.connection--red {
  stroke: #ff4444;
}

.connection--blue {
  stroke: #4444ff;
}

.connection--green {
  stroke: #44ff44;
}

.connection--yellow {
  stroke: #ffff44;
}

.connection--purple {
  stroke: #ff44ff;
}

.connection--orange {
  stroke: #ff8844;
}

/* Responsive design */
@media (max-width: 768px) {
  .connection__line {
    stroke-width: 3;
  }
  
  .connection--highlighted .connection__line {
    stroke-width: 5;
  }
  
  .connection__midpoint {
    r: 5;
  }
  
  .connection__interaction-area {
    stroke-width: 16;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .connection__line {
    stroke-width: 4;
  }
  
  .connection__midpoint {
    stroke-width: 2;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .connection {
    transition: none;
  }
  
  .connection__flow-indicator {
    animation: none;
  }
  
  .temp-connection__line {
    animation: none;
  }
  
  .temp-connection__endpoint {
    animation: none;
  }
  
  .connection--cycle {
    animation: none;
  }
}

/* Print styles */
@media print {
  .connection__flow-indicator {
    display: none;
  }
  
  .connection__interaction-area {
    display: none;
  }
}

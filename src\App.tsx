import React, { useEffect } from 'react';
import { GameProvider, useGame } from './context/GameContext';
import { GameArea } from './components/GameArea';
import { generateNodesForMode } from './utils/nodeGeneration';
import { GameActionType } from './context/GameContext';
import './App.css';

// Main game component
const Game: React.FC = () => {
  const { gameState, dispatch } = useGame();

  // Initialize game with some nodes
  useEffect(() => {
    const nodeSet = generateNodesForMode(
      gameState.mode,
      gameState.level,
      gameState.poolCapacity
    );

    // Add generated nodes to the game
    for (const node of nodeSet.nodes) {
      dispatch({
        type: GameActionType.ADD_NODE,
        payload: { node }
      });
    }
  }, [gameState.mode, gameState.level, dispatch]);

  return (
    <div className="game-container">
      <header className="game-header">
        <h1>Blueprint Puzzle Game</h1>
        <div className="game-controls">
          <button
            className="btn btn-primary"
            onClick={() => dispatch({ type: GameActionType.VALIDATE_BLUEPRINT })}
          >
            Validate Blueprint
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => dispatch({ type: GameActionType.RESET_GAME })}
          >
            Reset Game
          </button>
        </div>
      </header>

      <main className="game-main">
        <GameArea width={1200} height={800} />
      </main>

      <footer className="game-footer">
        <div className="game-stats">
          <span>Level: {gameState.level}</span>
          <span>Score: {gameState.score}</span>
          <span>Mode: {gameState.mode}</span>
          <span>Valid: {gameState.isValid ? '✓' : '✗'}</span>
        </div>
      </footer>
    </div>
  );
};

// App wrapper with provider
function App() {
  return (
    <GameProvider>
      <Game />
    </GameProvider>
  );
}

export default App;

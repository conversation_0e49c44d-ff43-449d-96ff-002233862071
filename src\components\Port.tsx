import React, { useState, useCallback } from 'react';
import { Port as PortType, PortShape, PortDirection } from '../types/game';
import { useGame } from '../context/GameContext';
import './Port.css';

interface PortProps {
  port: PortType;
  nodePosition: { x: number; y: number };
  onConnectionStart?: (port: PortType) => void;
  onConnectionEnd?: (port: PortType) => void;
  isHighlighted?: boolean;
  isValidTarget?: boolean;
}

export const Port: React.FC<PortProps> = ({
  port,
  nodePosition,
  onConnectionStart,
  onConnectionEnd,
  isHighlighted = false,
  isValidTarget = false
}) => {
  const { dragState, setDragState } = useGame();
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (port.direction === PortDirection.OUTPUT) {
      onConnectionStart?.(port);
      setDragState({
        isDragging: true,
        dragType: 'connection',
        draggedItem: { fromPortId: port.id },
        dragPosition: { x: e.clientX, y: e.clientY }
      });
    }
  }, [port, onConnectionStart, setDragState]);

  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (dragState.isDragging && 
        dragState.dragType === 'connection' && 
        port.direction === PortDirection.INPUT) {
      onConnectionEnd?.(port);
    }
  }, [dragState, port, onConnectionEnd]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const getPortShapeElement = () => {
    const size = 12;
    const strokeWidth = 2;
    const fill = port.color;
    const stroke = isHighlighted || isValidTarget ? '#ffffff' : '#333333';
    const strokeWidthFinal = isHighlighted || isValidTarget ? 3 : strokeWidth;

    switch (port.shape) {
      case PortShape.SQUARE:
        return (
          <rect
            x={-size / 2}
            y={-size / 2}
            width={size}
            height={size}
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidthFinal}
          />
        );

      case PortShape.CIRCLE:
        return (
          <circle
            cx={0}
            cy={0}
            r={size / 2}
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidthFinal}
          />
        );

      case PortShape.DIAMOND:
        const diamondPath = `M 0,${-size / 2} L ${size / 2},0 L 0,${size / 2} L ${-size / 2},0 Z`;
        return (
          <path
            d={diamondPath}
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidthFinal}
          />
        );

      case PortShape.TRIANGLE:
        const trianglePath = port.direction === PortDirection.OUTPUT
          ? `M ${-size / 2},${-size / 2} L ${size / 2},0 L ${-size / 2},${size / 2} Z`
          : `M ${size / 2},${-size / 2} L ${-size / 2},0 L ${size / 2},${size / 2} Z`;
        return (
          <path
            d={trianglePath}
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidthFinal}
          />
        );

      default:
        return (
          <circle
            cx={0}
            cy={0}
            r={size / 2}
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidthFinal}
          />
        );
    }
  };

  const getPortPosition = () => {
    return {
      x: nodePosition.x + port.position.x,
      y: nodePosition.y + port.position.y
    };
  };

  const portPosition = getPortPosition();
  
  const portClasses = [
    'port',
    `port--${port.direction}`,
    port.isConnected ? 'port--connected' : 'port--disconnected',
    isHighlighted ? 'port--highlighted' : '',
    isValidTarget ? 'port--valid-target' : '',
    isHovered ? 'port--hovered' : ''
  ].filter(Boolean).join(' ');

  return (
    <g
      className={portClasses}
      transform={`translate(${portPosition.x}, ${portPosition.y})`}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ cursor: port.direction === PortDirection.OUTPUT ? 'grab' : 'pointer' }}
    >
      {/* Port background circle for better interaction */}
      <circle
        cx={0}
        cy={0}
        r={16}
        fill="transparent"
        className="port__interaction-area"
      />
      
      {/* Port shape */}
      {getPortShapeElement()}
      
      {/* Connection indicator */}
      {port.isConnected && (
        <circle
          cx={0}
          cy={0}
          r={4}
          fill="#ffffff"
          opacity={0.8}
        />
      )}
      
      {/* Hover effect */}
      {isHovered && !port.isConnected && (
        <circle
          cx={0}
          cy={0}
          r={8}
          fill="none"
          stroke="#ffffff"
          strokeWidth={1}
          opacity={0.6}
        />
      )}
    </g>
  );
};

// Port validation utilities
export const getPortCompatibility = (port1: PortType, port2: PortType): boolean => {
  // Must be different directions
  if (port1.direction === port2.direction) return false;
  
  // Must be from different nodes
  if (port1.nodeId === port2.nodeId) return false;
  
  // Must have matching shape and color
  return port1.shape === port2.shape && port1.color === port2.color;
};

export const getValidTargetPorts = (
  sourcePort: PortType, 
  allPorts: PortType[]
): PortType[] => {
  return allPorts.filter(port => 
    getPortCompatibility(sourcePort, port) && !port.isConnected
  );
};

.port {
  transition: all 0.2s ease;
  cursor: pointer;
}

.port--input {
  /* Input ports on the left side */
}

.port--output {
  /* Output ports on the right side */
  cursor: grab;
}

.port--output:active {
  cursor: grabbing;
}

.port--connected {
  /* Connected ports have a subtle glow */
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
}

.port--disconnected {
  /* Disconnected ports are slightly dimmed */
  opacity: 0.9;
}

.port--highlighted {
  /* Highlighted ports (during connection) */
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
  transform: scale(1.2);
}

.port--valid-target {
  /* Valid connection targets */
  filter: drop-shadow(0 0 6px rgba(0, 255, 0, 0.6));
  animation: pulse-valid 1s infinite;
}

.port--hovered {
  /* Hovered ports */
  transform: scale(1.1);
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
}

.port__interaction-area {
  /* Invisible area for easier mouse interaction */
  pointer-events: all;
}

/* Animations */
@keyframes pulse-valid {
  0%, 100% {
    filter: drop-shadow(0 0 6px rgba(0, 255, 0, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(0, 255, 0, 0.9));
  }
}

@keyframes pulse-invalid {
  0%, 100% {
    filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(255, 0, 0, 0.9));
  }
}

/* Port shape specific styles */
.port--square {
  /* Square ports */
}

.port--circle {
  /* Circle ports */
}

.port--diamond {
  /* Diamond ports */
}

.port--triangle {
  /* Triangle ports */
}

/* Color-specific glows for better visual feedback */
.port[data-color="red"] {
  --port-glow-color: #ff4444;
}

.port[data-color="blue"] {
  --port-glow-color: #4444ff;
}

.port[data-color="green"] {
  --port-glow-color: #44ff44;
}

.port[data-color="yellow"] {
  --port-glow-color: #ffff44;
}

.port[data-color="purple"] {
  --port-glow-color: #ff44ff;
}

.port[data-color="orange"] {
  --port-glow-color: #ff8844;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .port {
    transform: scale(1.2);
  }
  
  .port--highlighted {
    transform: scale(1.4);
  }
  
  .port--hovered {
    transform: scale(1.3);
  }
}

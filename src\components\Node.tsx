import React, { useState, useCallback, useRef } from 'react';
import { Node as NodeType, NodeType as NodeTypeEnum, Port as PortType } from '../types/game';
import { Port } from './Port';
import { useGame } from '../context/GameContext';
import { GameActionType } from '../context/GameContext';
import './Node.css';

interface NodeProps {
  node: NodeType;
  onConnectionStart?: (port: PortType) => void;
  onConnectionEnd?: (port: PortType) => void;
  isSelected?: boolean;
  isDragging?: boolean;
  validTargetPorts?: string[];
}

export const NodeComponent: React.FC<NodeProps> = ({
  node,
  onConnectionStart,
  onConnectionEnd,
  isSelected = false,
  isDragging = false,
  validTargetPorts = []
}) => {
  const { dispatch, dragState, setDragState } = useGame();
  const [isHovered, setIsHovered] = useState(false);
  const dragStartPos = useRef<{ x: number; y: number } | null>(null);
  const nodeRef = useRef<SVGGElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    
    dragStartPos.current = {
      x: e.clientX - node.position.x,
      y: e.clientY - node.position.y
    };

    setDragState({
      isDragging: true,
      dragType: 'node',
      draggedItem: { nodeId: node.id },
      dragPosition: { x: e.clientX, y: e.clientY }
    });
  }, [node, setDragState]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (dragState.isDragging && 
        dragState.dragType === 'node' && 
        dragState.draggedItem?.nodeId === node.id &&
        dragStartPos.current) {
      
      const newPosition = {
        x: e.clientX - dragStartPos.current.x,
        y: e.clientY - dragStartPos.current.y
      };

      dispatch({
        type: GameActionType.MOVE_NODE,
        payload: { nodeId: node.id, position: newPosition }
      });

      setDragState({
        ...dragState,
        dragPosition: { x: e.clientX, y: e.clientY }
      });
    }
  }, [dragState, node.id, dispatch, setDragState]);

  const handleMouseUp = useCallback(() => {
    if (dragState.isDragging && dragState.dragType === 'node') {
      setDragState({
        isDragging: false,
        dragType: 'node',
        dragPosition: { x: 0, y: 0 }
      });
      dragStartPos.current = null;
    }
  }, [dragState, setDragState]);

  // Add global mouse event listeners when dragging
  React.useEffect(() => {
    if (dragState.isDragging && dragState.dragType === 'node' && dragState.draggedItem?.nodeId === node.id) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState, node.id, handleMouseMove, handleMouseUp]);

  const getNodeDimensions = () => {
    const baseWidth = 120;
    const baseHeight = 60;
    const portSpacing = 30;
    
    const maxPorts = Math.max(node.inputPorts.length, node.outputPorts.length);
    const height = Math.max(baseHeight, maxPorts * portSpacing + 20);
    
    return { width: baseWidth, height };
  };

  const getNodeColor = () => {
    switch (node.type) {
      case NodeTypeEnum.START:
        return '#4CAF50'; // Green
      case NodeTypeEnum.END:
        return '#F44336'; // Red
      case NodeTypeEnum.REGULAR:
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Gray
    }
  };

  const getNodeLabel = () => {
    switch (node.type) {
      case NodeTypeEnum.START:
        return 'START';
      case NodeTypeEnum.END:
        return 'END';
      case NodeTypeEnum.REGULAR:
        return 'NODE';
      default:
        return 'NODE';
    }
  };

  const { width, height } = getNodeDimensions();
  const nodeColor = getNodeColor();
  const nodeLabel = getNodeLabel();

  const nodeClasses = [
    'node',
    `node--${node.type}`,
    node.isPlaced ? 'node--placed' : 'node--in-pool',
    isSelected ? 'node--selected' : '',
    isDragging ? 'node--dragging' : '',
    isHovered ? 'node--hovered' : ''
  ].filter(Boolean).join(' ');

  return (
    <g
      ref={nodeRef}
      className={nodeClasses}
      transform={`translate(${node.position.x}, ${node.position.y})`}
      onMouseDown={handleMouseDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Node shadow */}
      <rect
        x={-width / 2 + 2}
        y={-height / 2 + 2}
        width={width}
        height={height}
        rx={8}
        fill="rgba(0, 0, 0, 0.2)"
        className="node__shadow"
      />
      
      {/* Node body */}
      <rect
        x={-width / 2}
        y={-height / 2}
        width={width}
        height={height}
        rx={8}
        fill={nodeColor}
        stroke={isSelected ? '#ffffff' : '#333333'}
        strokeWidth={isSelected ? 3 : 2}
        className="node__body"
      />
      
      {/* Node label */}
      <text
        x={0}
        y={0}
        textAnchor="middle"
        dominantBaseline="middle"
        fill="white"
        fontSize="12"
        fontWeight="bold"
        className="node__label"
      >
        {nodeLabel}
      </text>
      
      {/* Node ID (for debugging) */}
      <text
        x={0}
        y={12}
        textAnchor="middle"
        dominantBaseline="middle"
        fill="rgba(255, 255, 255, 0.7)"
        fontSize="8"
        className="node__id"
      >
        {node.id.slice(-4)}
      </text>
      
      {/* Input ports */}
      {node.inputPorts.map((port, index) => (
        <Port
          key={port.id}
          port={{
            ...port,
            position: {
              x: -width / 2 - 10,
              y: index * 30 - (node.inputPorts.length - 1) * 15
            }
          }}
          nodePosition={{ x: 0, y: 0 }}
          onConnectionStart={onConnectionStart}
          onConnectionEnd={onConnectionEnd}
          isValidTarget={validTargetPorts.includes(port.id)}
        />
      ))}
      
      {/* Output ports */}
      {node.outputPorts.map((port, index) => (
        <Port
          key={port.id}
          port={{
            ...port,
            position: {
              x: width / 2 + 10,
              y: index * 30 - (node.outputPorts.length - 1) * 15
            }
          }}
          nodePosition={{ x: 0, y: 0 }}
          onConnectionStart={onConnectionStart}
          onConnectionEnd={onConnectionEnd}
          isValidTarget={validTargetPorts.includes(port.id)}
        />
      ))}
      
      {/* Depth indicator (for debugging DAG) */}
      {node.depth !== undefined && (
        <text
          x={width / 2 - 10}
          y={-height / 2 + 15}
          textAnchor="middle"
          dominantBaseline="middle"
          fill="rgba(255, 255, 255, 0.8)"
          fontSize="10"
          className="node__depth"
        >
          D:{node.depth}
        </text>
      )}
      
      {/* Selection indicator */}
      {isSelected && (
        <rect
          x={-width / 2 - 5}
          y={-height / 2 - 5}
          width={width + 10}
          height={height + 10}
          rx={12}
          fill="none"
          stroke="#ffffff"
          strokeWidth={2}
          strokeDasharray="5,5"
          className="node__selection-indicator"
        />
      )}
    </g>
  );
};

// Helper component for node preview (in pool)
export const NodePreview: React.FC<{ node: NodeType; scale?: number }> = ({ 
  node, 
  scale = 0.7 
}) => {
  const { width, height } = {
    width: 120 * scale,
    height: 60 * scale
  };

  const getNodeColor = () => {
    switch (node.type) {
      case NodeTypeEnum.START:
        return '#4CAF50';
      case NodeTypeEnum.END:
        return '#F44336';
      case NodeTypeEnum.REGULAR:
        return '#2196F3';
      default:
        return '#9E9E9E';
    }
  };

  return (
    <svg width={width + 20} height={height + 20} className="node-preview">
      <g transform={`translate(${(width + 20) / 2}, ${(height + 20) / 2})`}>
        <rect
          x={-width / 2}
          y={-height / 2}
          width={width}
          height={height}
          rx={6}
          fill={getNodeColor()}
          stroke="#333333"
          strokeWidth={1}
        />
        
        <text
          x={0}
          y={0}
          textAnchor="middle"
          dominantBaseline="middle"
          fill="white"
          fontSize={10 * scale}
          fontWeight="bold"
        >
          {node.type.toUpperCase()}
        </text>
        
        {/* Port indicators */}
        {node.inputPorts.map((_, index) => (
          <circle
            key={`input-${index}`}
            cx={-width / 2 - 5}
            cy={index * 20 * scale - (node.inputPorts.length - 1) * 10 * scale}
            r={3 * scale}
            fill="#666"
          />
        ))}
        
        {node.outputPorts.map((_, index) => (
          <circle
            key={`output-${index}`}
            cx={width / 2 + 5}
            cy={index * 20 * scale - (node.outputPorts.length - 1) * 10 * scale}
            r={3 * scale}
            fill="#666"
          />
        ))}
      </g>
    </svg>
  );
};

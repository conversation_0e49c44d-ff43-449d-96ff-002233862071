import { 
  Node, 
  Connection, 
  ValidationResult, 
  NodeType,
  Port
} from '../types/game';
import { findPortInNode } from './gameUtils';

export interface DetailedValidationResult extends ValidationResult {
  nodeDepths: Map<string, number>;
  disconnectedNodes: string[];
  unreachableEndNodes: string[];
  invalidConnections: string[];
  portValidationErrors: Array<{
    portId: string;
    nodeId: string;
    error: string;
  }>;
}

// Enhanced DAG validation with detailed error reporting
export const validateDAGDetailed = (
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
): DetailedValidationResult => {
  const result: DetailedValidationResult = {
    isValid: true,
    errors: [],
    executionOrder: [],
    cycles: [],
    nodeDepths: new Map(),
    disconnectedNodes: [],
    unreachableEndNodes: [],
    invalidConnections: [],
    portValidationErrors: []
  };

  // Validate individual connections first
  validateConnections(nodes, connections, result);

  // Build adjacency lists
  const { adjacencyList, inDegree, reverseAdjacencyList } = buildGraphStructure(nodes, connections);

  // Detect cycles using DFS
  const cycleDetectionResult = detectCycles(adjacencyList);
  if (cycleDetectionResult.hasCycles) {
    result.isValid = false;
    result.cycles = cycleDetectionResult.cycles;
    result.errors.push('Graph contains cycles');
  }

  // Perform topological sort
  const topologicalResult = topologicalSort(adjacencyList, inDegree);
  result.executionOrder = topologicalResult.order;

  if (topologicalResult.order.length !== nodes.size) {
    result.isValid = false;
    result.errors.push('Not all nodes can be ordered (likely due to cycles)');
  }

  // Calculate node depths
  result.nodeDepths = calculateDepths(nodes, connections, result.executionOrder);

  // Find disconnected nodes
  result.disconnectedNodes = findDisconnectedNodes(nodes, connections);

  // Check reachability from start to end nodes
  result.unreachableEndNodes = findUnreachableEndNodes(nodes, adjacencyList, reverseAdjacencyList);

  // Validate that all start and end nodes are properly connected
  validateStartEndNodes(nodes, connections, result);

  // Final validation
  if (result.disconnectedNodes.length > 0) {
    result.isValid = false;
    result.errors.push(`Found ${result.disconnectedNodes.length} disconnected nodes`);
  }

  if (result.unreachableEndNodes.length > 0) {
    result.isValid = false;
    result.errors.push(`Found ${result.unreachableEndNodes.length} unreachable end nodes`);
  }

  return result;
};

// Validate individual connections
function validateConnections(
  nodes: Map<string, Node>,
  connections: Map<string, Connection>,
  result: DetailedValidationResult
): void {
  for (const [connectionId, connection] of connections) {
    const fromNode = nodes.get(connection.fromNodeId);
    const toNode = nodes.get(connection.toNodeId);

    if (!fromNode || !toNode) {
      result.invalidConnections.push(connectionId);
      result.errors.push(`Connection ${connectionId} references non-existent nodes`);
      continue;
    }

    const fromPort = findPortInNode(fromNode, connection.fromPortId);
    const toPort = findPortInNode(toNode, connection.toPortId);

    if (!fromPort || !toPort) {
      result.invalidConnections.push(connectionId);
      result.errors.push(`Connection ${connectionId} references non-existent ports`);
      continue;
    }

    // Validate port compatibility
    if (fromPort.shape !== toPort.shape || fromPort.color !== toPort.color) {
      result.invalidConnections.push(connectionId);
      result.errors.push(`Connection ${connectionId} connects incompatible ports`);
    }

    // Validate port directions
    if (fromPort.direction !== 'output' || toPort.direction !== 'input') {
      result.invalidConnections.push(connectionId);
      result.errors.push(`Connection ${connectionId} has invalid port directions`);
    }
  }
}

// Build graph data structures
function buildGraphStructure(
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
) {
  const adjacencyList = new Map<string, string[]>();
  const inDegree = new Map<string, number>();
  const reverseAdjacencyList = new Map<string, string[]>();

  // Initialize
  for (const [nodeId] of nodes) {
    adjacencyList.set(nodeId, []);
    reverseAdjacencyList.set(nodeId, []);
    inDegree.set(nodeId, 0);
  }

  // Build graph from valid connections
  for (const [, connection] of connections) {
    if (connection.isValid) {
      const fromNode = connection.fromNodeId;
      const toNode = connection.toNodeId;

      adjacencyList.get(fromNode)?.push(toNode);
      reverseAdjacencyList.get(toNode)?.push(fromNode);
      inDegree.set(toNode, (inDegree.get(toNode) || 0) + 1);
    }
  }

  return { adjacencyList, inDegree, reverseAdjacencyList };
}

// Detect cycles using DFS
function detectCycles(adjacencyList: Map<string, string[]>): {
  hasCycles: boolean;
  cycles: string[][];
} {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  const cycles: string[][] = [];

  function dfs(node: string, path: string[]): boolean {
    if (recursionStack.has(node)) {
      // Found a cycle
      const cycleStart = path.indexOf(node);
      if (cycleStart !== -1) {
        cycles.push(path.slice(cycleStart));
      }
      return true;
    }

    if (visited.has(node)) {
      return false;
    }

    visited.add(node);
    recursionStack.add(node);
    path.push(node);

    const neighbors = adjacencyList.get(node) || [];
    for (const neighbor of neighbors) {
      if (dfs(neighbor, [...path])) {
        return true;
      }
    }

    recursionStack.delete(node);
    return false;
  }

  let hasCycles = false;
  for (const [node] of adjacencyList) {
    if (!visited.has(node)) {
      if (dfs(node, [])) {
        hasCycles = true;
      }
    }
  }

  return { hasCycles, cycles };
}

// Topological sort using Kahn's algorithm
function topologicalSort(
  adjacencyList: Map<string, string[]>,
  inDegree: Map<string, number>
): { order: string[] } {
  const queue: string[] = [];
  const order: string[] = [];
  const inDegreeClone = new Map(inDegree);

  // Find nodes with no incoming edges
  for (const [nodeId, degree] of inDegreeClone) {
    if (degree === 0) {
      queue.push(nodeId);
    }
  }

  while (queue.length > 0) {
    const currentNode = queue.shift()!;
    order.push(currentNode);

    const neighbors = adjacencyList.get(currentNode) || [];
    for (const neighbor of neighbors) {
      const newDegree = (inDegreeClone.get(neighbor) || 0) - 1;
      inDegreeClone.set(neighbor, newDegree);

      if (newDegree === 0) {
        queue.push(neighbor);
      }
    }
  }

  return { order };
}

// Calculate node depths
function calculateDepths(
  nodes: Map<string, Node>,
  connections: Map<string, Connection>,
  executionOrder: string[]
): Map<string, number> {
  const depths = new Map<string, number>();

  for (const nodeId of executionOrder) {
    const node = nodes.get(nodeId);
    if (!node) continue;

    if (node.type === NodeType.START) {
      depths.set(nodeId, 0);
    } else {
      let maxInputDepth = -1;

      // Find maximum depth of input nodes
      for (const inputPort of node.inputPorts) {
        if (inputPort.isConnected) {
          for (const [, connection] of connections) {
            if (connection.toPortId === inputPort.id) {
              const sourceDepth = depths.get(connection.fromNodeId) || 0;
              maxInputDepth = Math.max(maxInputDepth, sourceDepth);
              break;
            }
          }
        }
      }

      depths.set(nodeId, maxInputDepth + 1);
    }
  }

  return depths;
}

// Find disconnected nodes
function findDisconnectedNodes(
  nodes: Map<string, Node>,
  connections: Map<string, Connection>
): string[] {
  const connectedNodes = new Set<string>();

  for (const [, connection] of connections) {
    connectedNodes.add(connection.fromNodeId);
    connectedNodes.add(connection.toNodeId);
  }

  const disconnected: string[] = [];
  for (const [nodeId, node] of nodes) {
    if (!connectedNodes.has(nodeId) && node.type === NodeType.REGULAR) {
      disconnected.push(nodeId);
    }
  }

  return disconnected;
}

// Find unreachable end nodes
function findUnreachableEndNodes(
  nodes: Map<string, Node>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>
): string[] {
  const startNodes: string[] = [];
  const endNodes: string[] = [];

  for (const [nodeId, node] of nodes) {
    if (node.type === NodeType.START) {
      startNodes.push(nodeId);
    } else if (node.type === NodeType.END) {
      endNodes.push(nodeId);
    }
  }

  const reachableFromStart = new Set<string>();

  // DFS from all start nodes
  function dfsForward(node: string) {
    if (reachableFromStart.has(node)) return;
    reachableFromStart.add(node);

    const neighbors = adjacencyList.get(node) || [];
    for (const neighbor of neighbors) {
      dfsForward(neighbor);
    }
  }

  for (const startNode of startNodes) {
    dfsForward(startNode);
  }

  // Find end nodes not reachable from any start node
  return endNodes.filter(endNode => !reachableFromStart.has(endNode));
}

// Validate start and end nodes
function validateStartEndNodes(
  nodes: Map<string, Node>,
  connections: Map<string, Connection>,
  result: DetailedValidationResult
): void {
  let hasStartNode = false;
  let hasEndNode = false;

  for (const [, node] of nodes) {
    if (node.type === NodeType.START) {
      hasStartNode = true;
      // Validate that start nodes have no input connections
      for (const inputPort of node.inputPorts) {
        if (inputPort.isConnected) {
          result.portValidationErrors.push({
            portId: inputPort.id,
            nodeId: node.id,
            error: 'Start node should not have input connections'
          });
        }
      }
    } else if (node.type === NodeType.END) {
      hasEndNode = true;
      // Validate that end nodes have no output connections
      for (const outputPort of node.outputPorts) {
        if (outputPort.isConnected) {
          result.portValidationErrors.push({
            portId: outputPort.id,
            nodeId: node.id,
            error: 'End node should not have output connections'
          });
        }
      }
    }
  }

  if (!hasStartNode) {
    result.isValid = false;
    result.errors.push('Blueprint must have at least one start node');
  }

  if (!hasEndNode) {
    result.isValid = false;
    result.errors.push('Blueprint must have at least one end node');
  }
}
